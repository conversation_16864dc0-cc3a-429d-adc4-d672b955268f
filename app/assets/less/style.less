// Load rest of the Bootstrap
@import 'bootstrap/mixins.less';
// @import 'bootstrap/print.less';
@import 'bootstrap/tables.less';
@import 'bootstrap/labels.less';
@import 'bootstrap/forms.less';
@import 'bootstrap/dropdowns.less';
@import 'bootstrap/popovers.less';
@import 'bootstrap/input-groups.less';
@import 'bootstrap/type.less';
@import 'bootstrap/utilities';
@import 'bootstrap/buttons';
@import 'bootstrap/modals';
@import 'bootstrap/list-group';
@import 'bootstrap/tooltip';
// Custom Framework
@import 'framework/core';
@import 'framework/grid';
@import 'framework/type';
@import 'framework/form';
@import 'framework/nav';
@import 'framework/button';
@import 'framework/helpers';
@import 'framework/components';
@import 'framework/react-anim';
@import 'framework/react-select';
// Third Party
@import (less) '../../thirdparty/add-to-homescreen/addtohomescreen.css';
@import 'thirdparty/react-slick.min.less';
@import 'thirdparty/react-slick-theme.min.less';
@import 'thirdparty/leaflet.contextmenu.less';
@import 'thirdparty/react-photoswipe.less';
// Components
@import 'components/alert';
@import 'components/spinner';
@import 'components/spinner_round';
@import 'components/photo_container';
@import 'components/react_autosuggest';
@import 'components/square_icon_btn';
@import 'components/custom_dropdown';
@import 'components/map_grid_toggle';
@import 'components/help_information';
@import 'components/close_btn';
@import 'components/add_to_homescreen';
@import 'components/agent_profile_image';
// Layout
@import 'layout/layout.less';
@import 'layout/map.less';
@import 'layout/parcelmap.less';
// Panels
@import 'panels/header.less';
@import 'panels/facebook-header.less';
@import 'panels/read-header.less';
@import 'panels/n-play-footer.less';
@import 'panels/footer.less';
@import 'panels/card.less';
@import 'panels/listing.less';
@import 'panels/listings.less';
@import 'panels/member-listings.less';
@import 'panels/menu.less';
@import 'panels/login.less';
@import 'panels/contact.less';
@import 'panels/agent-profile.less';
@import 'panels/sort.less';
@import 'panels/landing.less';
@import 'panels/home.less';
@import 'panels/share-form.less';
@import 'panels/results-help.less';
@import 'panels/help.less';
@import 'panels/facebook.less';
@import 'panels/agent-search.less';
@import 'panels/agent-notification.less';
@import 'panels/agent-listing-notification.less';
@import 'panels/employee-ui.less';
@import 'panels/home-worth-modal.less';
@import 'panels/dreamsweeps-modal.less';
@import 'panels/lending-tree-modal.less';
@import 'panels/pay-per-click-modal.less';
@import 'panels/rateplug-landing-modal.less';
@import 'panels/rateplug-home.less';

// Screens
@import 'screens/app.less';
@import 'screens/error.less';
@import 'screens/demo-expired.less';
@import 'screens/agent.less';
@import 'screens/grid.less';
@import 'screens/onboarding.less';
@import 'screens/map.less';
@import 'screens/buyer.less';
@import 'screens/logout.less';
@import 'screens/modals.less';
@import 'screens/listing.less';
@import 'screens/member-listings.less';
@import 'screens/member-search.less';
@import 'screens/homeWorth.less';
@import 'screens/login-prompter.less';
@import 'screens/buyer-info-modal.less';

.mobile{

  @header-height: 3em;

  &#app-container {
    padding-top: 0;
  }

  // Panels
  @import 'panels-mobile/listing.less';
  @import 'panels-mobile/listings.less';
  @import 'panels-mobile/photos.less';
  @import 'panels-mobile/header.less';
  @import 'panels-mobile/read-header.less';
  @import 'panels-mobile/search-bar.less';
  @import 'panels-mobile/agent-profile.less';
  @import 'panels-mobile/map.less';
  @import 'panels-mobile/n-play-footer.less';
  @import 'panels-mobile/card.less';
  @import 'panels-mobile/home.less';
  @import 'panels-mobile/landing.less';
  @import 'panels-mobile/member-listings.less';
  @import 'panels-mobile/agent-search.less';
  @import 'panels-mobile/map-grid-toggle.less';
  @import 'panels-mobile/home-worth-modal.less';
  @import 'panels-mobile/dreamsweeps-modal.less';
  @import 'panels-mobile/rateplug-home.less';

  // Screens
  @import 'screens-mobile/agent.less';
  @import 'screens-mobile/buyer.less';
  @import 'screens-mobile/map.less';
  @import 'screens-mobile/error.less';
  @import 'screens-mobile/demo-expired.less';
  @import 'screens-mobile/homeWorth.less';
  @import 'screens-mobile/member-listings.less';
  @import 'screens-mobile/member-search.less';
}

//modals are children of the body, so they won't be under the .mobile app-container
@import 'panels-mobile/login.less';
@import 'panels-mobile/share-form.less';
@import 'panels-mobile/contact.less';

@import 'demo.less';
