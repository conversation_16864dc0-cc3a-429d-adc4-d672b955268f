@import (multiple) "./rateplug.less";

.layout--header {
  & {
    background-color: #fff;
    color: #000;
  }

  .header-left {
    background-color: #fff;

    div.agent-container {
      padding-left: 200px;
      position: relative;

      &:hover {
        background-color: #EAEAEA;
      }

      &:before {
        content: '';
        display: block;
        position: absolute;
        top: 0.5rem;
        left: 0;
        bottom: 0.5rem;
        background-image: url(/images/fairway.svg);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center center;
        width: 200px;
      }
    }

    div.agent-container .agent-header-image {
      border: 3px solid @brand-primary;
      width: 4.5rem;
      height: 4.5rem;
      transform: scale(1.2);

      div.pro-badge-container {
        top: -5px;
        left: -10px;
        width: 20px;
        height: 20px;
      }
    }

    div.agent-container div.header-agent-dropdown {
      background-color: @brand-darkblue;

      ul li:hover {
        background-color: @brand-primary;
      }
    }

    div.agent-container div.dropdown-chevron svg {
      fill: @brand-primary;
      stroke: @brand-primary;
    }

    div.agent-container div.agent-texts.full-name {
      padding-left: 7rem;
    }

    div.agent-container div.agent-texts.full-name p {
      color: #000;
    }

    div.controls-container.search-button-container > div.search-container .search-form .react-autosuggest__container input {
      background-color: #EAEAEA;
    }

    div.agent-container div.header-agent-dropdown {
      left: 0;
      width: 100%;
    }
  }

  .header-right {
    .direct-login-button {
      color: #000;

      &:hover {
        color: #fff;
      }
    }

    .tag-control {
      fill: @brand-primary;

      &:hover, &:active {
        fill: #fff;
      }
    }

    .help-control {
      color: @brand-primary;

      .help-icon {
        border-color: @brand-primary;
      }

      &:hover, &:active {
        color: #fff;

        .help-icon {
          border-color: #fff;
        }
      }
    }
  }
}

.rateplug-landing-next, .rateplug-landing-last {
  background-image: url(https://nplayassets.blob.core.windows.net/search2/rateplug/rateplug-onboarding-next-more.png) !important;
}

.n-play-footer > div {
  background: @brand-primary-darker;
}

.grid-container.lights-out {
  background-color: #101010;

  .card .card-front {
    background-color: #003A70;

    .bottom {
      border: none;
    }
  }
}

div.pieSlice3 > div.pie,
#mortgage-calculator-popover .modal-dialog .popover-content p.legend-label span.legend-icon.legend3 {
  background: #F5B93C !important;
}

.rateplug-filters-wrapper .rateplug-filters-container {
  padding-left: 0 !important;
}


.mobile {
  div.landing div.landing-wrapper-container {
    padding-top: 10em;

    div.agent-info {
      position: relative;

      &:before {
        top: -65px;
        color: white;
        font-size: 35px;
        font-weight: 600;
      }
    }
  }
}

div.landing div.landing-wrapper-container {
  padding-top: 7em;

  div.agent-info {
    position: relative;

    &:before {
      content: 'All Roads Lead Home';
      position: absolute;
      top: -100px;
      left: 0;
      right: 0;
      text-align: center;
      color: white;
      font-size: 45px;
      font-weight: 600;
    }
  }

  div.links {
    left: 0;
    right: 0;
    background: #fff;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 1.5em;
    height: 7rem;

    p a {
      color: @brand-primary;
    }

    &:before {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      background-image: url(/images/fairway.svg);
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center center;
      height: 6rem;
      width: 200px;
    }
  }

  div.mobile-landing-footer {
    background-color: @brand-darkblue;
  }
}

div.landing button[type="submit"] {
  background-color: @brand-specialfinancing !important;
}

.mobile {
  .search-bar .search-container {
    form.search-form {
      .search-field-icon-left {
        background-image: url("/images/fairway-favicon.png");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 2em 2em;

        svg {
          visibility: hidden;
        }
      }
    }
  }

  .mobile-rateplug-subheader .rateplug-filters-container {
    padding-left: 65px !important;
  }

  div.mobile-slideout > div.agent-area-bottom .share-agent svg {
    fill: @brand-primary;
    stroke: @brand-primary;
  }

  div.mobile-slideout > div.agent-area-bottom > ul > li {
    > svg {
      fill: @brand-primary;
      stroke: @brand-primary;
    }
    > span {
      color: @brand-primary;
    }
  }
}

.card-image {
  filter: none !important;
}

p, span, .yelp-local {
  font-family: "Roboto Serif", serif !important;
}

.sort-header,
.btn,
.editing-filters-container
{

  &, & p, & span {
    font-family: "Montserrat", sans-serif !important;
  }
}

// TODO hide cookie banner for fairway theme temporarily
div#termsfeed-com---nb {
  display: none !important;
}

.saletype-nav > a:nth-child(2),
.custom-dropdown.sale_type {
  display: none !important;
}