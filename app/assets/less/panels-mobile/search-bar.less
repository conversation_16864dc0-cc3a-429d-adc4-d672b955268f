.search-bar{
  height: 3em;
  z-index: 11;
  width: 100%;
  position: fixed;
  top: 0;
  transition: transform 0.5s ease-out 0s;
  transform: translateY(0%);
  background-color: transparent;

  &.agent-layout{
    &.scroll-hide{
      .pull-down{
        opacity: 0.8;
        transition: transform 0.5s ease-in 0s, opacity 0.5s ease-in 0s;
        transform: translate(-50%, 275%);
      }
    }
  }

  &.scroll-hide{
    transition: transform 0.5s ease-in 0s;
    transform: translateY(-350%);
    .pull-down{
      opacity: 0.8;
      transition: transform 0.5s ease-in 0s, opacity 0.5s ease-in 0s;
      transform: translate(-50%, 275%);
    }
    .react-autosuggest__suggestions-container {
      display: none;
    }
  }

  .editing-filters-container{
    display: none;
  }

  .pull-down{
    background-color: @brand-red;
    width: 3em;
    height: 2.5em;
    opacity: 0;
    transition: transform 0.5s ease-out 0s, opacity 0.25s ease-out 0s;
    transform: translate(-50%, -100%);
    left: 50%;
    position: absolute;
    text-align: center;
    padding: 6px;
    z-index: -1;
    cursor: pointer;
    svg{
      fill: #fff;
      width: 1.75em;
      height: 1.75em;
      margin: 0 auto;
      stroke-width: 0px;
    }
  }

  @keyframes hideResults {
    to {
      opacity: 0;
      height: 0;
    }
  }

  .results{
    text-align: center;
    z-index: 10;
    left: 0;
    background-color: @brand-blue;
    color: #fff;
    border-radius: 0;
    font-size: 12px;
    padding: 0;
    height: @search-bar-results-height;
    line-height: @search-bar-results-height;
    margin: 0 7px;
    opacity: .85;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.15);
    animation: hideResults .25s ease-in 5s forwards;
    -webkit-appearance: none;
  }

  .search-container{
    height: 3.5em;
  }

  .search-bar-controls{
    height: 2.5em;
    display: flex;
    background-color: @accent-gray;
    justify-content: space-between;
    padding: 0 1em 0 1em;

    &>div{
      flex: 0 0 auto;
      text-align: center;
      line-height: 2.5em;
      text-transform: uppercase;
      font-weight: 600;
      color: @brand-red;
      cursor: pointer;
    }

    .toggle-view{
      &.disabled{
        color: #ccc;
      }
    }
  }

  &.compressed{
    .search-container{
//      border-bottom: 1px solid #ebebeb;
    }
    .search-bar-controls{
      display: none;
    }
    .results{
      display: none;
    }
  }

  .search-form{
    z-index: 11;

    .react-autosuggest__container{
      padding: 0.5em;
      padding-bottom: 0;
      position: relative;

      .react-autosuggest__suggestions-container{
        position: absolute;
        top: 100%;
        left: 0;
        margin-left: 0.5em;
        margin-right: 0.5em;
        width: calc(~"100% - 1em");
        z-index: 12;
        border: none;
      }

      input{
        background-color: #fff;
        border-radius: 0;
        font-weight: 400;
        font-size: 14px;
        line-height: 2em;
        height: 3em;
        padding-left: 3em;
        padding-right: 3em;
        text-overflow: ellipsis;
        box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.15);
        border: none;
        -webkit-appearance: none;
      }
    }

    .search-field-icons {
      height: 3em;
      width: 3em;
      position: absolute;
      background-color: #fff;
      padding: 10px;
      z-index: 1;
      svg{
        fill: @brand-primary;
        height: 100%;
        width: 100%;
      }

      &.search-field-icon-left {
        left: 0.5em;
        top: 0.5em;
      }
      &.search-field-icon-right {
        right: 0.5em;
        top: 0.5em;

        display: none;
      }
      &.search-field-icon-sort {
        right: 2.7em;
        top: 0.5em;

        display: none;
      }
    }

    &.on-map {
      .search-field-icons.search-field-icon-right {
        display: block;
      }
    }

    &.on-grid {
      .react-autosuggest__container input {
        padding-right: 4.5em;
      }
      .search-field-icons.search-field-icon-right,
      .search-field-icons.search-field-icon-sort {
        display: block;
      }
    }
  }

  .agent-header-mobile {
    margin: 5px 0.5em 0;
    padding: 8px 0;
    z-index: 11;
    position: relative;
    height: 60px;

    .agent-container {
      width: 0px;

      .agent-image {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        position: absolute;
        top: 0;
        left: 0;
        cursor: pointer;
        box-shadow: 0px 1px 15px rgba(0,0,0,0.35);

        div.pro-badge-container {
          top: 0;
          left: -6px;
          width: 24px;
          height: 24px;
        }
      }

      p {
        color: #fff;
        margin: 0;
        font-size: 12px;
        display: none;
      }

      .icon-license {
        width: 2em;
        height: 2em;
        position: absolute;
        top: 50%;
        margin-top: -1em;
        right: 0.5em;
        fill: #fff;
        display: none;
      }

      &.show-agent {
        width: auto;
        background: @brand-darkblue;
        margin-left: 31px;
        padding: 5px 36px;

        p, svg {
          display: block;
        }
      }
    }
  }

  .sort-container {
    z-index: 12;
    position: relative;
    margin-top: -60px;
    margin-left: 0.5em;
    margin-right: 0.5em;

    .list-group {
      width: calc(~"100% - 1em");
    }
  }
}


.search-bar.editing-filters{

  .editing-filters-container{
    display: block;
    margin-top: -60px;
    margin-left: 0.5em;
    margin-right: 0.5em;
    z-index: 12;
    position: relative;
  }

  .search-bar-controls{
    display: none;
  }

  .editing-filters-controls{
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    background-color: #fff;
    padding-top: 1em;
    padding-left: 2em;
    padding-right: 2em;
    padding: 1em 2em 0.5em;

    & > h3{
      flex: 1 0 auto;
      text-transform: uppercase;
      color: @brand-primary-dark;
      padding-left: 1em;
      font-weight: 600;
      font-size: 16px;
    }

    .editing-filters-reset{
      flex: 0 1 auto;
      color: @brand-primary;
      text-transform: uppercase;
      margin-right: 1em;

      svg{
        fill: @brand-primary;
        height: 16px;
        width: 16px;
        position: relative;
        top: 0.2em;
        margin-right: 5px;
      }
    }

    & > button{
      flex: 0 1 auto;
      font-size: 12px;
      line-height: 2em;
      padding: 0 8px;
      text-transform: uppercase;
    }
  }

  .filters-scroll-container {
    position: relative;
    background-color: #fff;
    width: 100%;
    height: calc(~"100vh - 165px");
    z-index: 1;
    overflow-y: auto;
    padding: 0 1em;

    .filters {
      padding-bottom: 1em;

      .active-toggle {

        button {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: @brand-primary-dark;
          text-align: left;
          width: 100%;
          border: none;
          white-space: normal !important;
          font-size: 110%;
          text-transform: none;
          font-weight: 400;
          padding: 6px 8px 6px 16px;

          svg {
            width: 2rem;
            height: 2rem;
            transform: translateY(-3px);

            &.icon-checkbox-unchecked {
              fill: #000;
            }
          }
        }
      }

      .custom-dropdown {
        display: block;
        background-color: #fff;
        .btn-group {
          width: 100%;
          background-color: #fff;
        }
        .btn {
          color: @brand-primary-dark;
          text-align: left;
          width: 100%;
          border: none;
          white-space: normal !important;
          font-size: 110%;
          text-transform: none;
          font-weight: 400;

          .caret {
            float: right;
            margin-top: 0.5em;
          }
        }
      }

      .dropdown-menu {
        position: relative;
        border: none;
        box-shadow: none;
        width: 100%;

        a {
          border-radius: @border-radius-small;
        }
      }

      .custom-dropdown.not-empty {
        .btn {
          color: @brand-red;
        }
      }

      p {
        margin: 0.5em 1em 0.2em;
      }

      .keywords-select {
        margin: 0.5em 1em 1em;

        .Select-control {
          border-color: @icon-gray;
          border-radius: @border-radius-small;
          cursor: text;

          &:hover, &:focus, &:active, &.active {
            color: #000;
            background-color: #f5f5f5;
          }

          .Select-placeholder {
            height: 100%;
          }

          .Select-item {
            border-radius: 1em;
            padding: 0 0.25em;
            color: @brand-primary;
            background-color: #fafafa;

            .Select-item-icon {
              float: right;
              border: none;

              &:hover {
                color: @brand-primary;
                font-weight: bold;
                background-color: transparent;
              }
            }
          }

          .Select-arrow-zone, .Select-arrow {
            display: none;
          }

          input {
            color: @brand-primary;
          }
        }

        // Hide Dropdown for now
        .Select-menu-outer {
          border-radius: 0;

          .Select-noresults {
            display: none;
          }

          .Select-option {

            &.is-focused {
              background-color: #f5f5f5;
            }
          }
        }
      }

      .search-by-payment-list-price-toggle {
        padding: 0;

        .toggle-container {
          display: flex;
          justify-content: space-between;
          align-items: center;

          color: @brand-primary;
          width: 100%;
          white-space: normal;
          font-size: 110%;
          padding: 0 1em 0.25em;
        }

        .toggle-label {
          text-transform: none;
          font-weight: 400;
        }

        .toggle-buttons {
          display: flex;
          overflow: hidden;
          gap: 6px;

          .toggle-btn {
            pointer-events: none;
            font-weight: 400;

            &:not(.selected) {
              background: #E6E6E6;
              color: #000;
              pointer-events: all;
              border: none;
            }
          }
        }
      }
    }
  }
}

.mobile-rateplug-subheader {
  overflow-x: scroll;

  .rateplug-filters-container, .idx-filters-container {
    justify-content: flex-start;
    padding-left: 65px;

    &.on-grid {
      .filters {
        .btn {
          background-color: #EBEBEB;
          color: @brand-primary;
        }
        .info-button {
          svg {
            fill: @brand-primary;
            stroke: @brand-primary;
          }
        }
      }
    }
  }
}