const React = require('react');
const ReactDOM = require('react-dom');
const classNames = require('classnames');
const mixins = require('../lib/mixins');

// Panels

//  Header = require('./panels/header/index'),
const SearchBar = require('./panels-mobile/search-bar/index');
const READHeader = require('./panels/read-header');
//  Menu = require('./panels/menu/index'),
//  Leftbar = require('./panels/leftbar/index'),
//  MapPanel = require('./panels/map/index'),
//  Sub = require('./panels/sub/index'),
//  Photos = require('./panels/photos/index'),
const AgentContact = require('./panels/contact/agent-contact');
const LoanOfficerContact = require('./panels/contact/loan-officer-contact');
const ShareForm = require('./panels/share/index');
const BrokerageModal = require('./panels/agent-profile/brokerage');
const BlankModal = require('./panels/modals/blank');
const ResultsHelp = require('./panels/results-count/help');
const DemoBar = require('./panels/demo-bar');

// Screens
const Onboarding = require('./screens/onboarding/index');
const Home = require('./screens/home/<USER>');
//  Agent = require('./screens/agent/index'),
//  Listing = require('./screens/listing/index'),
//  Grid = require('./screens/grid/index'),
//  Map = require('./screens/map/index'),
//  Tagging = require('./screens/tagging/index'),
//  Featured = require('./screens/featured/index'),
//  Buyer = require('./screens/buyer/index'),
const Logout = require('./screens/logout/index');
const HomeWorth = require('./screens/home-worth');
const MemberSearch = require('./screens/member-search');
const MemberListings = require('./screens/member-listings');

// Agent = require('./screens/agent/index'),
const Error = require('./screens/error/index');
const DemoExpired = require('./screens/demo-expired/index');
const ModalPopup = require('./screens/modal-popup');
const LoginPrompter = require('./screens/login-prompter/index');
const BuyerInfoModal = require('./screens/buyer-info-modal/index');
const AgentListingNotification = require('./panels/agent-listing-notification');

const Facebook = require('./screens/facebook/index');

// Panels-Mobile
const MapPanel = require('./panels-mobile/map/index');
const Photos = require('./panels-mobile/photos/index');
const Header = require('./panels-mobile/header/index');
const MapGridToggle = require('./panels-mobile/map-grid-toggle/index');

// Screens-Mobile
const Agent = require('./screens-mobile/agent');
const Buyer = require('./screens-mobile/buyer');
const Map = require('./screens-mobile/map');
const AgentSearch = require('./panels/agent-search');
const Grid = require('./screens-mobile/grid');
const Landing = require('./screens-mobile/landing/index');
const Listing = require('./screens-mobile/listing/index');

const HomeWorthModal = require('./panels/home-worth-modal');
const DreamsweepsModal = require('./panels/dreamsweeps-modal');
const LendingTreeModal = require('./panels/lending-tree/modal');
const PayPerClickModal = require('./panels/pay-per-click/modal');
const RateplugSpecialFinancingMissingModal = require('./panels/rateplug-landing-modal/special-financing-missing');
const RateplugSpecialFinancingModal = require('./panels/rateplug-landing-modal/special-financing');
const RateplugLandingModal = require('./panels/rateplug-landing-modal');
const RateplugWelcomeModal = require('./panels/rateplug-welcome-modal');
const RateplugHomeButton = require('./panels/rateplug-home');

const App = React.createClass({

  mixins: [mixins.cursors],

  cursors: {
    blur: ['shared', 'blurContent'],
    agentSearchLayout: ['layout', 'agentSearch'],
    specialFinancing: ['shared', 'menu', 'specialFinancing'],
  },

  render() {
    return (
      <div className={classNames('',
        `${this.state.specialFinancing ? `--highlight:SpecialFinancePrograms__${this.state.specialFinancing}` : ''}`,
        {
          blur: this.state.blur || this.state.agentSearchLayout,
        })}
      >
        <DemoBar />
        <AgentListingNotification />
        <Header />
        <SearchBar />
        <READHeader />
        <Photos />
        <MapPanel />

        <Onboarding />
        <Landing />
        <Home />
        <Agent />

        <Buyer />

        <Listing />

        <Grid />
        <Map />
        <HomeWorth />
        <Facebook />

        <MemberSearch />
        <MemberListings />

        {/* <Tagging/> */}
        {/* <Featured/> */}

        {/* <Menu/> */}

        <Logout />
        <AgentContact mobile />
        <LoanOfficerContact mobile />
        <ShareForm mobile />
        <BrokerageModal />

        <LoginPrompter randomizeModals />
        <BuyerInfoModal />

        <Error />
        <DemoExpired />

        <ModalPopup />

        <BlankModal />
        <ResultsHelp />
        <AgentSearch />
        <MapGridToggle />
        <HomeWorthModal />
        <DreamsweepsModal />
        <LendingTreeModal />
        <PayPerClickModal />
        <RateplugLandingModal />
        <RateplugSpecialFinancingModal />
        <RateplugSpecialFinancingMissingModal />
        <RateplugHomeButton />
        <RateplugWelcomeModal />
      </div>
    );
  },
});

module.exports = function (app, callback) {
  const appContainer = document.getElementById('app-container');
  appContainer.classList.add('mobile');

  ReactDOM.render(
    <App />,
    appContainer,
    callback,
  );
};
