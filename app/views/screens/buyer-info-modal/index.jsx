const React = require('react');
const Modal = require('react-bootstrap').Modal;
const mixins = require('../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'screens.buyer-info-modal',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors],

  cursors: {
    layout: ['shared', 'login', 'buyerInfoModal'],
    buyerData: ['shared', 'buyer', 'data'],
  },

  getInitialState() {
    return {
      phoneError: null,
      processing: false,
    };
  },

  close() {
    this.actions.login.hideBuyerInfoModal();
  },

  emailInput() {
    if (this.state.buyerData.Email) {
      return null;
    }

    return (
      <div className="mt5">
        <p>To serve you the best we can please provide us with your contact information. We will send you updates of listings that match your latest search criteria.</p>
        <label htmlFor="buyer-info-email">Email</label>
        <input id="buyer-info-email" className="form-control" type="email" placeholder="Email" name="email" />
      </div>
    );
  },

  phoneInput() {
    if (this.state.buyerData.Phone) {
      return null;
    }

    return (
      <div className="mt5">
        <label htmlFor="buyer-info-phone">Phone</label>
        <input id="buyer-info-phone" className="form-control" type="text" placeholder="(*************" name="phone" />
        {this.state.phoneError ? <small className="text-danger">Please double check your phone number, with area code it should be 10 digits.</small> : null}
      </div>
    );
  },

  thanks() {
    return <p>Thanks! You&apos;re all set!</p>;
  },

  formSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {
      Email: formData.get('email') || undefined,
      Phone: formData.get('phone') || undefined,
    };

    if (data.Phone) {
      if (data.Phone.replace(/[^0-9]/g, '').length === 10) {
        data.Phone = data.Phone.replace(/[^0-9]/g, '');
      } else {
        return this.setState({ phoneError: true });
      }
    }

    this.setState({ processing: true });
    this.actions.login.putBuyer(data, () => {
      this.setState({ processing: false });
    });
  },

  render() {
    if (!this.state.layout || !this.state.buyerData) {
      return null;
    }

    return (
      <div>
        <Modal className="buyer-info-modal" show={!!this.state.layout} onHide={this.close}>
          <Modal.Header>
            <Modal.Title>Need Information</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {
              (this.state.buyerData.Email && this.state.buyerData.Phone)
                ? (
                  <div>
                    {this.thanks()}
                    <div className="text-right mt10">
                      <button type="button" className="btn btn-primary mr5" onClick={this.close}>
                        Done
                      </button>
                    </div>
                  </div>
                )
                : (
                  <form onSubmit={this.formSubmit}>
                    {this.emailInput()}
                    {this.phoneInput()}
                    <div className="text-right mt15">
                      <button type="button" className="btn btn-default mr5" onClick={this.close}>
                        Not Now
                      </button>
                      <button type="submit" className="btn btn-primary mrn" disabled={this.state.processing}>
                        {this.state.processing ? 'Processing...' : 'Submit'}
                      </button>
                    </div>
                  </form>
                )
            }
          </Modal.Body>
        </Modal>
      </div>
    );
  },
});
