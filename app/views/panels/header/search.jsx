const React = require('react');
const ReactCSSTransitionGroup = require('react-addons-css-transition-group');
const classNames = require('classnames');
const _ = require('lodash');
const mixins = require('../../../lib/mixins');

const SVGIcon = require('../../components/svg_icon');
const SaleTypeDropdown = require('../menu/search-components/sale-type');
const SearchByPaymentListPriceToggle = require('../menu/search-components/search-by-payment-listing-price');
const CarDropdown = require('../menu/search-components/car');
const LevelsDropdown = require('../menu/search-components/levels');
// const LotSizeDropdown = require('../menu/search-components/lot-size');
const AgeDropdown = require('../menu/search-components/age');
const SquareFeetDropdown = require('../menu/search-components/square-feet');
const BedBathDropdown = require('../menu/search-components/bed-bath');
const PriceDropdown = require('../menu/search-components/price');
const MortgagePaymentDropdown = require('../menu/search-components/mortgage-payment');
const DownPaymentDropdown = require('../menu/search-components/down-payment');
const SpecialFinancingDropdown = require('../menu/search-components/special-financing');
const PropertyTypesDropdown = require('../menu/search-components/property-types');
const Keywords = require('../menu/search-components/keywords');
const ActiveToggle = require('../menu/search-components/active-toggle');
const SearchField = require('../menu/search-components/search-field');
const ResultsText = require('../menu/search-components/results-text');
// const Sort = require('../grid-menu/sort');

module.exports = React.createClass({

  displayName: 'panel.header.search',

  mixins: [mixins.debug, mixins.actions, mixins.cursors, mixins.router],

  cursors: {
    menuOn: ['layout', 'menu'],
    showEditingFilters: ['shared', 'showFilters'],
    viewingGrid: ['layout', 'grid', 'grid'],
    viewingMap: ['layout', 'map'],
    viewingMemberListings: ['layout', 'memberListings'],
    // we listen to listings so we update when they change, to check if we should display the terms
    mapGridListings: ['panels', 'listings', 'data'],
    featuredListings: ['screens', 'featured', 'data'],
    taggedListings: ['screens', 'tagging', 'data', 'Listings'],
  },

  getInitialState() {
    return { showTerms: false };
  },

  facets: {
    filterCount: ['filterCount'],
  },

  componentDidUpdate() {
    if ((!(this.state.viewingMap || this.state.viewingGrid || this.state.viewingMemberListings)) && this.state.showEditingFilters) {
      this.actions.menu.setEditingFilters(false);
    }
    this.checkForTermsDisplay();
  },

  // loop through displayed MLSs and if any of them require terms to be shown, display it
  checkForTermsDisplay() {
    const mlsArray = this.actions.common.getAllVisibleMlsArray();

    const found = _.find(mlsArray, (mls) => mls.IsSearchLicenseAgreementShown);

    if (this.state.showTerms != found) {
      this.setState({ showTerms: found });
    }
  },

  filterPanel() {
    if (!this.state.showEditingFilters) {
      return null;
    }

    return (
      <div className="editing-filters-container">
        <div className="editing-filters-controls">
          <ResultsText />
          <span role="button" tabIndex="-1" aria-label="Reset search" className="editing-filters-reset" onClick={this.resetSearch}>
            <SVGIcon name="icon-reset" />
            Reset
          </span>
          <button type="button" className="btn btn-primary" onClick={this.doneEditingFilters}>Done</button>
        </div>

        <div className="filters">
          <p className="mt10">Use keywords to Search</p>
          <Keywords />
          <SearchByPaymentListPriceToggle />
          <SaleTypeDropdown />
          <PriceDropdown />
          <DownPaymentDropdown />
          <MortgagePaymentDropdown />
          <BedBathDropdown />
          <PropertyTypesDropdown />
          <SquareFeetDropdown />
          <AgeDropdown />
          {/* <LotSizeDropdown /> */}
          <LevelsDropdown />
          <CarDropdown />
          <SpecialFinancingDropdown />
          <ActiveToggle />
        </div>
      </div>
    );
  },

  filterOverlay() {
    if (!this.state.showEditingFilters) {
      return null;
    }

    return (
      <div
        role="button"
        tabIndex="-1"
        aria-label="Apply filters"
        className="editing-filters-overlay"
        onTouchStart={this.doneEditingFilters}
        onMouseDown={this.doneEditingFilters}
      />
    );
  },

  searchSubmit(e) {
    e.preventDefault();
    this.actions.common.flagUserAsInteractedWithSite();
    const inputs = e.target && e.target.getElementsByClassName('form-control');
    const input = inputs && inputs.length > 0 ? inputs[0] : null;
    if (input) {
      const suggestions = this.refs.SearchField.state.suggestions;
      input.blur();
      if (suggestions && suggestions[0] && suggestions[0].type === 'mlsListingIdMatch') {
        this.refs.SearchField.autosuggestSelected(e, { suggestion: suggestions[0] });
      } else {
        this.refs.SearchField.searchKeywordEntered();
      }
    }
  },

  resetSearch() {
    this.actions.menu.resetMenuSelections();
  },

  filtersClicked() {
    if (this.state.showEditingFilters) {
      this.doneEditingFilters();
    } else {
      this.startEditingFilters();
    }
  },

  startEditingFilters() {
    this.actions.common.disableBodyScroll();
    this.actions.menu.setEditingFilters(true);
  },

  doneEditingFilters() {
    this.actions.common.enableBodyScroll();
    this.actions.menu.setEditingFilters(false);
  },

  render() {
    return (
      <div className="controls-container search-button-container">
        <div className="filter-container">
          <a
            role="button"
            tabIndex="-1"
            className={classNames('btn btn-lg btn-primary', {
              disabled: !(this.state.viewingMap || this.state.viewingGrid || this.state.viewingMemberListings),
            })}
            title="Filters"
            onClick={this.filtersClicked}
          >
            FILTER
            <span className="label-search"> SEARCH</span>
            &nbsp;
            +
            {this.state.filterCount || 0}
          </a>
        </div>

        <div className="search-container">
          <SearchField ref="SearchField" onSubmit={this.searchSubmit} hideAddresses={this.props.hideAddresses} />
          {this.state.showTerms
            ? (
              <div className="terms-notice">
                <a
                  role="button"
                  tabIndex="-1"
                  className="implicit-agree"
                  onClick={this.actions.common.toggleModal.bind(null, 'Terms')}
                >
                  By continuing you agree to the terms and conditions
                </a>
              </div>
            )
            : null}
        </div>

        <ReactCSSTransitionGroup transitionName="fade" transitionEnterTimeout={500} transitionLeaveTimeout={500}>
          {this.filterOverlay()}
        </ReactCSSTransitionGroup>

        <ReactCSSTransitionGroup transitionName="slide-left" transitionEnterTimeout={500} transitionLeaveTimeout={500}>
          {this.filterPanel()}
        </ReactCSSTransitionGroup>
      </div>
    );
  },
});
