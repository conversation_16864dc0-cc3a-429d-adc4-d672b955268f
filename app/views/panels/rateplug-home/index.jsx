const React = require('react');
const mixins = require('../../../lib/mixins');
const SVGIcon = require('../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panels.rateplug-home',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.router, mixins.cursors],

  cursors: {
    activeId: ['panels', 'listings', 'activeId'],
    layout: ['layout'],
  },

  getInitialState() {
    return {
    };
  },

  componentDidMount() {
  },

  goHome() {
    console.log('Posting \'[RATEPLUG]GO_HOME\' to parent');
    this.actions.analytics.sendEvent('rateplug', 'home button', 'click');
    window.parent.postMessage('[RATEPLUG]GO_HOME', '*');
  },

  goToRateplug() {
    if (document.body.dataset.theme === 'more') {
      window.open(`${window.CONFIG.IN_PROD ? 'https://more.mutualmortgage.com' : 'https://mutualmortgage-test.rateplug.com'}/capture.asp?guid=${window.rateplug.rp_buyer}`, '_self');
    } else if (document.body.dataset.theme === 'afordal') {
      window.open(`${window.CONFIG.IN_PROD ? 'https://app.afordal.com' : 'https://test.afordal.com'}/capture.asp?guid=${window.rateplug.rp_buyer}`, '_self');
    } else if (document.body.dataset.theme === 'fairway') {
      window.open(`${window.CONFIG.IN_PROD ? 'https://fairway.afordal.com' : 'https://fairway.afordal.com'}/capture.asp?guid=${window.rateplug.rp_buyer}`, '_self');
    }
  },

  shouldHideComponent() {
    const isRateplugModal = this.state.layout.rateplugLandingModal || this.state.layout.rateplugWelcomeModal;
    const isMobile = this.utils.useMobileSite();
    const isShowingListing = !!this.state.activeId;

    if (isRateplugModal) {
      return true;
    }

    if (isMobile && isShowingListing) {
      return true;
    }

    return false;
  },

  render() {
    if (this.shouldHideComponent()) {
      return null;
    }

    // Only showing under rateplug more theme using css
    if (this.utils.inIFrame()) {
      return (
        <div role="button" tabIndex="-1" aria-label="Close" className="rateplug-home" onClick={this.goHome}>
          <SVGIcon name="icon-home" />
          <p>My Current Home</p>
        </div>
      );
    }

    if (window.rateplug.rp_buyer) {
      return (
        <div role="button" tabIndex="-1" aria-label="Close" className="rateplug-home" onClick={this.goToRateplug}>
          <SVGIcon name="icon-home" />
          <p>My Current Home</p>
        </div>
      );
    }

    return null;
  },

});
