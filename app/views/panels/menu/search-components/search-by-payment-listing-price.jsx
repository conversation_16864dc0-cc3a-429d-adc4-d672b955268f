const React = require('react');
const classNames = require('classnames');
const mixins = require('../../../../lib/mixins');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.search-by-payment-listing-price',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  shouldShow() {
    if (!window.rateplug.rp_buyer) {
      return false;
    }

    // Show this toggle ONLY when either condition is met:
    // - window.rateplug.search_by_payment exists, OR
    // - SEARCH_BY_PAYMENT_QS key exists in session storage
    return !!window.rateplug.search_by_payment
           || !!window.sessionStorageAlias.getItem('SEARCH_BY_PAYMENT_QS');
  },

  getCurrentValue() {
    // If window.rateplug.search_by_payment exists → "payment" is selected
    // If window.rateplug.search_by_payment does not exist → "list_price" is selected
    return window.rateplug && window.rateplug.search_by_payment ? 'payment' : 'list_price';
  },

  onPaymentClick() {
    if (this.getCurrentValue() !== 'payment') {
      this.switchToSearchByPayment();
    }
  },

  onPriceClick() {
    if (this.getCurrentValue() !== 'list_price') {
      this.switchToRegularSearch();
    }
  },

  switchToRegularSearch() {
    const currentValue = window.rateplug && window.rateplug.search_by_payment ? 'payment' : 'list_price';
    if (currentValue !== 'list_price') {
      this.actions.analytics.sendEvent('rateplug', 'toggle', 'to-regular-search');
      window.sessionStorageAlias.setItem('SEARCH_BY_PAYMENT_QS', window.location.search);
      window.location.search = `rp_buyer=${window.rateplug.rp_buyer}&rp_downpayment=${window.rateplug.rp_downpayment}&downPayment=${window.rateplug.rp_downpayment}&theme=${document.body.dataset.theme || 'rateplug'}&saleType=1`;
    }
  },

  switchToSearchByPayment() {
    const currentValue = window.rateplug && window.rateplug.search_by_payment ? 'payment' : 'list_price';
    if (currentValue !== 'payment') {
      this.actions.analytics.sendEvent('rateplug', 'toggle', 'to-search-by-payment');
      const qs = window.sessionStorageAlias.getItem('SEARCH_BY_PAYMENT_QS');
      window.sessionStorageAlias.removeItem('SEARCH_BY_PAYMENT_QS');
      window.location.search = qs;
    }
  },

  render() {
    if (!this.shouldShow()) {
      return null;
    }

    const currentValue = this.getCurrentValue();

    return (
      <div className={classNames('search-by-payment-list-price-toggle', this.props.className)}>
        <div className="toggle-container">
          <span className="toggle-label">Search By:</span>
          <div className="toggle-buttons" role="group">
            <button
              type="button"
              className={classNames('btn btn-sm btn-primary toggle-btn', {
                selected: currentValue === 'payment',
              })}
              onClick={this.onPaymentClick}
              title="Search by monthly payment"
            >
              Payment
            </button>
            <button
              type="button"
              className={classNames('btn btn-sm btn-primary toggle-btn', {
                selected: currentValue === 'list_price',
              })}
              onClick={this.onPriceClick}
              title="Search by listing price"
            >
              Price
            </button>
          </div>
        </div>
      </div>
    );
  },

});
