const React = require('react');
const classNames = require('classnames');
const Autosuggest = require('react-autosuggest');
const _ = require('lodash');
const mixins = require('../../../../lib/mixins');
const SVGIcon = require('../../../components/svg_icon');

module.exports = React.createClass({

  displayName: 'panel.menu.search-components.search-field',

  mixins: [mixins.debug, mixins.actions, mixins.utils, mixins.cursors, mixins.router, mixins.pureRender],

  cursors: {
    locationQuery: ['panels', 'listings', 'meta', 'locationQuery'],
  },

  getInitialState() {
    return {
      suggestions: [],
      value: '',
    };
  },

  componentDidMount() {
    if (!this.actions.common.isLocationQueryPlaceholder()) {
      this.setState({ value: this.state.locationQuery });
    }
    this.refs.autosuggest.input.addEventListener('keyup', this.resetLocationQuery);
  },

  componentWillUpdate(nextProps, nextState) {
    if (this.state.locationQuery != nextState.locationQuery && nextState.locationQuery) {
      this.setState({ value: nextState.locationQuery });
    }
  },

  componentWillUnmount() {
    this.refs.autosuggest.input.removeEventListener('keyup', this.resetLocationQuery);
  },

  wipeField() {
    if (this.state.value) {
      this.setState({ value: '', suggestions: [] });
    }
    this.refs.autosuggest.input.placeholder = 'Address, Neighborhood, School, City, Zip';
  },

  renderSuggestion(data/* , inputVal */) {
    if (data.value == 'Current Location') {
      return (
        <span className="current-location">
          <SVGIcon name={this.utils.isIos() ? 'icon-location-ios' : 'icon-center'} className="autosuggest-icon" />
          {data.value}
        </span>
      );
    }
    if (data.location) {
      if (data.location.locType === 'C') {
        return (
          <span>
            <SVGIcon name="icon-map" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
      if (data.location.locType === 'S') {
        return (
          <span>
            <SVGIcon name="icon-school-hat" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
      if (data.location.locType === 'N' || data.location.locType === 'Z') {
        return (
          <span>
            <SVGIcon name="icon-map-pin" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
      if (data.location.locType === 'A') {
        return (
          <span>
            <SVGIcon name="icon-front-of-house" className="autosuggest-icon" />
            {data.value}
          </span>
        );
      }
    }
    return <span>{data.value}</span>;
  },

  autosuggestSelected(e, selection) {
    const data = selection.suggestion;
    if (data && data.location) {
      // Save to Saved Searches if School
      if (data.location.locType === 'S') {
        this.actions.common.setLocationCustomName(data.location.lat, data.location.lon, data.value);
      }

      this.actions.menu.onAutosuggest.onNav(
        data.location.lat, data.location.lon, data.location.radius, data.value, {
          listingId: data.location.locType === 'A' && data.location.locId,
          locType: data.location.locType,
          locId: data.location.locId,
        },
      );
    } else if (data && data.searchHistory) {
      const searchQuery = [
        data.searchHistory.Latitude,
        data.searchHistory.Longitude,
        data.searchHistory.Distance,
      ].join(',');
      const opts = this.utils._paramsToOpts(data.searchHistory.Filters);

      this.actions.menu.resumeSearchHistory(data.searchHistory.AgentId,
        data.searchHistory.CustomName || data.searchHistory.SearchText,
        searchQuery, opts, new Date(data.searchHistory.LastSearchDate),
        data.searchHistory.Theme, data.searchHistory.RateplugBuyerId);
    } else if (data && data.value === 'Current Location') {
      setTimeout(() => {
        this.setState({ value: 'Loading Location...' });
      }, 0);
      this.actions.menu.searchCurrentLocation(this.props.saleType, (err) => {
        if (err) {
          this.setState({ value: 'Geolocation failed.' });
        }
      });
    }

    setTimeout(() => {
      (this.refs.autosuggest && this.refs.autosuggest.input) ? this.refs.autosuggest.input.blur() : '';
    }, 100);
  },

  resetLocationQuery() {
    if (this.state.locationQuery) {
      this.actions.common.saveLocationQuery('', true);
    }
  },

  searchKeywordEntered(e, suggestion) {
    const autoSuggestValue = _.get(suggestion, 'focusedSuggestion.value') || (this.state && this.state.value);

    if (this.actions.common.isLocationQueryPlaceholder()) {
      return;
    }

    if (this.actions.menu.onAutosuggest.nlpLocationIdentified) {
      return;
    }

    if (this.state && (this.state.value === 'Loading Location...')) {
      return;
    }

    if ((!autoSuggestValue) && this.state.locationQuery) {
      this.setState({ value: this.state.locationQuery });
      return;
    }

    if (autoSuggestValue !== this.state.locationQuery) {
      this.actions.menu.geocodeAddress(autoSuggestValue, () => {
      });
    }
  },

  loadSuggestions(value) {
    const fetchSuggestions = this.props.hideAddresses
      ? this.actions.menu.onAutosuggest.api_noAddresses
      : this.actions.menu.onAutosuggest.api;

    fetchSuggestions(value, (err, results) => {
      this.setState({ suggestions: results || [] });
    });
  },

  onSuggestionsFetchRequested({ value }) {
    this.loadSuggestions(value);
  },

  onSuggestionsClearRequested() {
    this.setState({
      suggestions: [],
    });
  },

  onChange(event, { newValue }) {
    this.setState({
      value: newValue,
    });
  },

  render() {
    return (
      <form
        className={classNames('search-form', this.props.className)}
        onSubmit={this.props.onSubmit ? this.props.onSubmit : null}
      >
        <div className="input-container">
          <Autosuggest
            ref="autosuggest"
            inputProps={{
              type: 'search',
              autoFocus: false, //! this.state.locationQuery && !this.utils.isPhoneSizedScreen(),
              className: 'form-control',
              placeholder: this.actions.common.isLocationQueryPlaceholder()
                ? this.state.locationQuery : 'Address, Neighborhood, School, City, Zip',
              value: this.actions.common.isLocationQueryPlaceholder(this.state.value) ? '' : this.state.value || '',
              onBlur: () => {
                setTimeout(this.searchKeywordEntered, 100);
              },
              onFocus: () => {
                this.actions.agent.hideAgentHeaderDropdown();
                this.loadSuggestions('');
              },
              onChange: this.onChange,
            }}
            suggestions={this.state.suggestions}
            shouldRenderSuggestions={() => true}
            onSuggestionsFetchRequested={this.onSuggestionsFetchRequested}
            onSuggestionsClearRequested={this.onSuggestionsClearRequested}
            onSuggestionSelected={this.autosuggestSelected}
            getSuggestionValue={(data) => data.value}
            renderSuggestion={this.renderSuggestion}
            scrollBar
            focusInputOnSuggestionClick={false}
            alwaysRenderSuggestions={false}
          />
          <div role="button" tabIndex="-1" aria-label="Search" className="search-field-icon" onClick={this.searchSubmit}>
            <SVGIcon name="icon-search" />
          </div>
        </div>
      </form>
    );
  },

});
