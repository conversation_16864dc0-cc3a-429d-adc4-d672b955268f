const React = require('react');
const classNames = require('classnames');
const _ = require('lodash');
const defer = require('lodash.defer');
const DropdownButton = require('react-bootstrap').DropdownButton;
const MenuItem = require('react-bootstrap').MenuItem;
const mixins = require('../../lib/mixins');

module.exports = React.createClass({

  displayName: 'components.custom_dropdown',

  mixins: [mixins.debug, mixins.utils, mixins.pureRender],

  getInitialState() {
    return {
      activePanel: 1,
      open: false,
    };
  },

  forceOpenOnce: false,
  forceCloseOnce: false,

  componentDidMount() {
    this.updateInputs(this.props);
  },

  componentWillReceiveProps(nextProps) {
    this.updateInputs(nextProps);
  },

  updateInputs(props) {
    // if (true /*! props.inputReadOnly */) {
    if (this.refs.input1) {
      if (props.currentItem1) {
        this.refs.input1.value = this.utils.getNumberFromString(props.currentItem1);
      } else {
        this.refs.input1.value = '';
      }
    }
    if (this.refs.input2) {
      if (props.currentItem2) {
        this.refs.input2.value = this.utils.getNumberFromString(props.currentItem2);
      } else {
        this.refs.input2.value = '';
      }
    }
    // }
  },

  onSelect1(e/* , href, target */) {
    if (typeof e === 'object') {
      e.preventDefault();
    } else {
      console.log('Selected: ');
      console.log(this.props.options1[e]);
      this.setState({
        activePanel: 2,
      });
      if (!this.utils.isMobile()) {
        this.refs.input2 && this.refs.input2.focus();
      }
      defer(() => {
        this.props.onOptionChange1 ? this.props.onOptionChange1(this.props.options1[e]) : '';
      });
    }
  },

  onSelect2(e) {
    if (typeof e === 'object') {
      e.preventDefault();
    } else {
      console.log('Selected: ');
      console.log(this.props.options2[e]);
      defer(() => {
        this.props.onOptionChange2 ? this.props.onOptionChange2(this.props.options2[e]) : '';
      });
    }
  },

  onClickDoNothing(e) {
    if (typeof e === 'object') {
      e.stopPropagation();
      e.preventDefault();
    }
    if (this.props.options2) { // Remain Option when its a multi-select
      this.forceOpenOnce = true;
    }
  },

  onClickDoNothingClose(e) {
    if (typeof e === 'object') {
      e.stopPropagation();
      e.preventDefault();
    }
    this.setState({ open: false });
  },

  onDropdownButtonClick(e) {
    this.onClickDoNothing(e);
    if (this.state.activePanel !== 1) {
      this.setState({ activePanel: 1 });
    }
    if ((!this.refs.dropdown.state) || (this.refs.dropdown.state.open !== true)) {
      // Opening

      defer(() => {
        if (!this.utils.isMobile()) {
          this.refs.input1 ? this.refs.input1.focus() : '';
        }
      });
    }
  },

  onClickInput1(e) {
    this.onClickDoNothing(e);
    if (this.state.activePanel !== 1) {
      this.setState({ activePanel: 1 });
    }
  },

  onClickInput2(e) {
    this.onClickDoNothing(e);
    if (this.state.activePanel !== 2) {
      this.setState({ activePanel: 2 });
    }
  },

  onKeyDownInput1(e) {
    switch (e.keyCode) {
      case 9:
        this.forceOpenOnce = true;
        break;
      case 13:
        this.forceCloseOnce = true;
        this.setState({ open: false });
        break;
      default: break;
    }
  },

  onKeyDownInput2(e) {
    switch (e.keyCode) {
      case 9:
        this.forceOpenOnce = true;
        break;
      case 13:
        this.forceCloseOnce = true;
        this.setState({ open: false });
        break;
      default: break;
    }
  },

  onBlurInput1(e) {
    this.props.onBlurInput1 ? this.props.onBlurInput1(e) : '';
  },

  onBlurInput2(e) {
    this.props.onBlurInput2 ? this.props.onBlurInput2(e) : '';
  },

  shouldBeActive1(item) {
    // console.log("shouldBeActive1 " + JSON.stringify(item) + " " + this.props.currentItem1)
    return this.props.shouldBeActive1 ? this.props.shouldBeActive1(item) : false;
  },

  shouldBeActive2(item) {
    return this.props.shouldBeActive2 ? this.props.shouldBeActive2(item) : false;
  },

  shouldBeDisabled1(item) {
    return this.props.shouldBeDisabled1 ? this.props.shouldBeDisabled1(item) : false;
  },

  shouldBeDisabled2(item) {
    return this.props.shouldBeDisabled2 ? this.props.shouldBeDisabled2(item) : false;
  },

  getDisplayString() {
    return this.props.displayString && this.props.displayString(this.state) || '';
  },

  inputRow() {
    if (this.props.inputTitle1 && !this.props.inputTitle2) {
      return (
        <MenuItem eventKey="input" onClick={this.onClickDoNothing} draggable={false}>
          <input
            className="form-control"
            type={this.props.inputType ? this.props.inputType : 'text'}
            ref="input1"
            placeholder={this.props.inputTitle1}
            onKeyDown={this.onKeyDownInput1}
            onBlur={this.onBlurInput1}
            onClick={this.onClickDoNothing}
            readOnly={this.props.inputReadOnly}
          />
        </MenuItem>
      );
    }
    if (this.props.inputTitle1 && this.props.inputTitle2) {
      return (
        <MenuItem eventKey="input" onClick={this.onClickDoNothing} draggable={false}>
          <input
            className="form-control col-11-24"
            type={this.props.inputType ? this.props.inputType : 'text'}
            ref="input1"
            placeholder={this.props.inputTitle1}
            onKeyDown={this.onKeyDownInput1}
            onBlur={this.onBlurInput1}
            onClick={this.onClickInput1}
            readOnly={this.props.inputReadOnly}
          />
          <div className="col-2-24" />
          <input
            className="form-control col-11-24"
            type={this.props.inputType ? this.props.inputType : 'text'}
            ref="input2"
            placeholder={this.props.inputTitle2}
            onKeyDown={this.onKeyDownInput2}
            onBlur={this.onBlurInput2}
            onClick={this.onClickInput2}
            readOnly={this.props.inputReadOnly}
          />
        </MenuItem>
      );
    }
    return null;
  },

  optionsRows() {
    if (this.props.options1 && !this.props.options2) {
      return (
        this.props.options1.map(function (obj, index) {
          return (
            <MenuItem
              eventKey={index}
              key={index}
              active={this.shouldBeActive1(obj)}
              onSelect={this.onSelect1}
              onClick={this.onClickDoNothing}
            >
              {obj}
            </MenuItem>
          );
        }, this)
      );
    }
    if (this.props.options1 && this.props.options2) {
      if (this.state.activePanel === 1) {
        return (
          this.props.options1.map(function (obj, index) {
            return (
              <MenuItem
                eventKey={index}
                key={index}
                active={this.shouldBeActive1(obj)}
                disabled={this.shouldBeDisabled1(obj)}
                onSelect={this.onSelect1}
                onClick={this.onClickDoNothing}
              >
                {obj}
              </MenuItem>
            );
          }, this)
        );
      }
      if (this.state.activePanel === 2) {
        return (
          this.props.options2.map(function (obj, index) {
            return (
              <MenuItem
                eventKey={index}
                key={index}
                className="text-right"
                active={this.shouldBeActive2(obj)}
                disabled={this.shouldBeDisabled2(obj)}
                onSelect={this.onSelect2}
                onClick={this.onClickDoNothingClose}
              >
                {obj}
              </MenuItem>
            );
          }, this)
        );
      }
    }
    return null;
  },

  handleOnToggle(open) {
    // if the flag is set and it's trying to close, keep it open
    if (this.forceCloseOnce) {
      this.setState({ open: false });
      this.forceCloseOnce = false;
    } else if (!open && this.forceOpenOnce) {
      this.setState({ open: true });
      this.forceOpenOnce = false;
    } else {
      this.setState({ open });
    }

    return false;
  },

  render() {
    const componentClass = classNames(
      'custom-dropdown',
      {
        active: this.props.active,
        'not-empty': this.props.currentItem1 || this.props.currentItem2,
      },
      this.props.className,
      _.snakeCase(this.props.title || this.getDisplayString()),
    );

    return (
      <div className={componentClass}>
        <DropdownButton
          id={`${this.props.title || this.getDisplayString()}`}
          bsSize="large"
          ref="dropdown"
          title={this.props.displayInPlace ? this.getDisplayString() : this.props.title}
          onClick={this.onDropdownButtonClick}
          onToggle={this.handleOnToggle}
          open={this.state.open}
        >

          {this.inputRow()}

          {
            this.props.inputTitle1
              ? <MenuItem divider /> : null
            }

          {this.optionsRows()}

        </DropdownButton>
        {
        this.props.displayInPlace ? null
          : (
            <div className="display">
              <p className="text-center">{this.getDisplayString()}</p>
            </div>
          )
        }
      </div>
    );
  },

});
