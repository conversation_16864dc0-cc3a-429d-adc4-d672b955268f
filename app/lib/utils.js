const map = require('lodash.map');
const forEach = require('lodash.foreach');
const extend = require('extend');
const sortOptions = require('./sort-options.js');

const constants = require('./constants');
const propertyTaxByCounty = require('./property-tax-by-county.js');
const stateLongToShort = require('./state-long-to-short.js');

module.exports = function (app) {
  /** *
   * Static methods for Utils
   * */
  function Utils() {
  }

  Utils.clientIsMobile = null;

  Utils.emptyImageUrl = 'data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=';

  // temporary hack solution per gary
  Utils.replaceCaliforniaMlsWithCarets = function (url, mlsIds, propertyListingId) {
    const matches = url.match(/\/ca(claw|crisnet|itech|palm|vcrds)\//i);
    if (!matches || matches.length < 2) {
      return url;
    }

    // Use other MLS if there's a non-carets listing in mlsIds Array
    for (let i = 1; i < mlsIds.length; i++) {
      if (!mlsIds[i].match(/^ca(claw|crisnet|itech|palm|vcrds)$/i)) {
        return url.replace(mlsIds[0], mlsIds[i]);
      }
    }

    return url.replace(matches[0], '/carets/').replace(propertyListingId, `${propertyListingId}_${matches[1]}`);
  };

  Utils.getImageUrls = function (o, mainImageOnly) {
    if (!o) {
      return [];
    }

    // === Build a complete collection based on screen size

    // Formula used 900 + 320 = 1220 (right listings bar size)
    let screenSize = Math.max((window.innerWidth
      || document.documentElement.clientWidth
      || document.body.clientWidth || 1220) - 280);

    screenSize = Math.max(280, screenSize - (screenSize % 4));

    if (Utils.useMobileSite()) {
      screenSize = Math.max(Math.max(window.innerHeight, window.innerWidth), screenSize);
    }
    // var screenSizeHeight = screenSize * ratio;
    screenSize = Utils.imageSizeWidth(screenSize);

    o.DefaultImageNotAvailable = !o.DefaultImage;

    if (mainImageOnly) {
      return Utils.generateMainImageUrl(o);
    }

    if (!o.DefaultImage) {
      const gsvImage = Utils.generateMainImageUrl(o, screenSize);
      return [{
        sm: gsvImage,
        lg: gsvImage,
      }];
    }
    for (let i = 0; i < o.Images.length; i++) {
      const smWidth = 60;
      const lgWidth = Math.min(1900, screenSize);
      const xlgWidth = Math.min(1900, screenSize * 3);
      o.Images[i].sm = Utils.getResizedImage(o.Images[i].ImageURL, smWidth, o.ImageLocation);
      o.Images[i].lg = Utils.getResizedImage(o.Images[i].ImageURL, lgWidth, o.ImageLocation);
      o.Images[i].xlg = Utils.getResizedImage(o.Images[i].ImageURL, xlgWidth, o.ImageLocation);
    }

    return o.Images;
  };

  Utils.generateMainImageUrl = function (o, width) {
    if ((!width) || (!Number.isInteger(width))) {
      width = 280;
    }

    // const ratio = 0.6;

    if (!o.DefaultImage) {
      // var googleImage = String().concat(
      //  "https://maps.googleapis.com/maps/api/streetview?size=",
      //  "" + width, "x", "" + Math.ceil(width * ratio),
      //  "&location=",
      //  o.Location.Lat, ",", o.Location.Lon,
      //  "&fov=80&pitch=-0.76",
      //  "&key=" + window.CONFIG.GOOGLE_KEY);
      //
      // return googleImage;
      return Utils.streetViewNotAvailableImage();
    }

    return Utils.getResizedImage(o.DefaultImage, width, o.ImageLocation);
  };

  Utils.imageSizeWidth = function (incomingWidth = 280) {
    for (const widthBreakpoint of [60, 280, 480, 720, 1024, 1280, 1440, 1900, 1920, 2356]) {
      if (incomingWidth <= widthBreakpoint) {
        return widthBreakpoint;
      }
    }
    return incomingWidth;
  };

  Utils.getResizedImage = function (url, width, imageLocation = 'NAS') {
    if (imageLocation === 'OSS') {
      return `${url}?x-oss-process=image/resize,w_${width}/interlace,1`;
    }
    return `${url}${width}`;
  };

  // Rateplug
  Utils.getRateQueryParameters = function () {
    const qsParams = {};

    // Do not pass dp/ir/nm when coming from rateplug and window.rateplug.search_by_payment is falsy
    if (window.rateplug.rp_buyer && !window.rateplug.search_by_payment) {
      qsParams.dp = undefined;
      qsParams.ir = undefined;
      qsParams.nm = undefined;
      return qsParams;
    }

    const opts = app.actions.menu.getSearchSelectionsOpts();

    qsParams.dp = opts.downPayment || (window.rateplug.rp_buyer ? '0' : undefined);
    // Interest Rate
    qsParams.ir = Number(window.rateplug.rp_rate || window.localStorageAlias.getItem('mortgage-calculator.interestRate') || window.idx_rate || '7.62');

    if (qsParams.ir) {
      qsParams.nm = 360;
    }

    if (window.rateplug.rp_va_loan_limit) {
      qsParams.sf_va_loan_limit = +window.rateplug.rp_va_loan_limit;
    }

    return qsParams;
  };

  Utils._optsToParams = function (opts) {
    const qsParams = {};

    opts.ct ? qsParams.ct = opts.ct : '';
    opts.rm ? qsParams.rm = opts.rm : '';

    opts.mlsId ? qsParams.ml = opts.mlsId : '';
    opts.mlsAgentId ? qsParams.ma = opts.mlsAgentId : '';
    opts.mlsOfficeId ? qsParams.mo = opts.mlsOfficeId : '';
    opts.mlsListingId ? qsParams.mi = opts.mlsListingId : '';

    opts.saleType
      ? qsParams.sa = opts.saleType : '';
    opts.homeTypesArray && opts.homeTypesArray.length > 0
      ? qsParams.tp = opts.homeTypesArray : '';
    opts.sortType && opts.sortType in sortOptions
      ? qsParams.st = sortOptions[opts.sortType] : '';
    opts.maxPrice
      ? qsParams.lt = opts.maxPrice : '';
    opts.minPrice
      ? qsParams.gt = opts.minPrice : '';
    opts.minBeds
      ? qsParams.bd = opts.minBeds : '';
    opts.minBaths
      ? qsParams.bt = opts.minBaths : '';
    opts.minYear
      ? qsParams.ny = opts.minYear : '';
    opts.maxYear
      ? qsParams.xy = opts.maxYear : '';
    opts.minLotSizeSqft
      ? qsParams.nl = opts.minLotSizeSqft : '';
    opts.maxLotSizeSqft
      ? qsParams.xl = opts.maxLotSizeSqft : '';
    opts.minLivingSqft
      ? qsParams.nq = opts.minLivingSqft : '';
    opts.maxLivingSqft
      ? qsParams.xq = opts.maxLivingSqft : '';
    opts.minGarage
      ? qsParams.ng = opts.minGarage : '';
    opts.maxGarage
      ? qsParams.xg = opts.maxGarage : '';
    opts.minStories
      ? qsParams.ns = opts.minStories : '';
    opts.maxStories
      ? qsParams.xs = opts.maxStories : '';
    if (!qsParams.xs) {
      if (qsParams.ns == '1') {
        qsParams.xs = qsParams.ns;
      }
    }
    opts.keywords && opts.keywords.length > 0
      ? qsParams.att = opts.keywords : '';
    opts.statusEq
      ? qsParams.se = opts.statusEq : '';
    opts.specialFinancing && opts.specialFinancing.length > 0
      ? qsParams.sf = opts.specialFinancing : '';

    if (opts.saleType !== 2) {
      opts.maxMortgagePayment
        ? qsParams.hp = opts.maxMortgagePayment : '';
      opts.minMortgagePayment
        ? qsParams.lp = opts.minMortgagePayment : '';
      qsParams.dp = opts.downPayment || (window.rateplug.rp_rate ? '0' : undefined);
      // Interest Rate
      qsParams.ir = Number(window.rateplug.rp_rate || window.localStorageAlias.getItem('mortgage-calculator.interestRate') || window.idx_rate || '7.62');
      // Payment period
      qsParams.nm = 360;
    }

    return { ...qsParams, ...Utils.getRateQueryParameters() };
  };

  Utils._paramsToOpts = function (qsParams) {
    const opts = {};

    qsParams.ct ? opts.ct = qsParams.ct : '';

    qsParams.ml ? opts.mlsId = qsParams.ml : '';
    qsParams.ma ? opts.mlsAgentId = qsParams.ma : '';
    qsParams.mo ? opts.mlsOfficeId = qsParams.mo : '';
    qsParams.mi ? opts.mlsListingId = qsParams.mi : '';

    qsParams.sa
      ? opts.saleType = qsParams.sa : '';
    qsParams.tp && qsParams.tp.length > 0
      ? opts.homeTypesArray = qsParams.tp : '';
    // qsParams.sortType && sortOptions.hasOwnProperty(qsParams.sortType) ?
    //  opts.st = sortOptions[qsParams.sortType] : ''
    qsParams.lt
      ? opts.maxPrice = qsParams.lt : '';
    qsParams.gt
      ? opts.minPrice = qsParams.gt : '';
    qsParams.hp
      ? opts.maxMortgagePayment = qsParams.hp : '';
    qsParams.lp
      ? opts.minMortgagePayment = qsParams.lp : '';
    qsParams.dp
      ? opts.downPayment = qsParams.dp : '';
    qsParams.bd
      ? opts.minBeds = qsParams.bd : '';
    qsParams.bt
      ? opts.minBaths = qsParams.bt : '';
    qsParams.ny
      ? opts.minYear = qsParams.ny : '';
    qsParams.xy
      ? opts.maxYear = qsParams.xy : '';
    qsParams.nl
      ? opts.minLotSizeSqft = qsParams.nl : '';
    qsParams.xl
      ? opts.maxLotSizeSqft = qsParams.xl : '';
    qsParams.nq
      ? opts.minLivingSqft = qsParams.nq : '';
    qsParams.xq
      ? opts.maxLivingSqft = qsParams.xq : '';
    qsParams.ng
      ? opts.minGarage = qsParams.ng : '';
    qsParams.xg
      ? opts.maxGarage = qsParams.xg : '';
    qsParams.ns
      ? opts.minStories = qsParams.ns : '';
    qsParams.xs
      ? opts.maxStories = qsParams.xs : '';
    qsParams.att && qsParams.att.length > 0
      ? opts.keywords = qsParams.att : '';
    qsParams.se
      ? opts.statusEq = qsParams.se : '';
    qsParams.sf && qsParams.sf.length > 0
      ? opts.specialFinancing = qsParams.sf : '';

    return opts;
  };

  Utils.validateLocationStr = function (locationStr) {
    if (!locationStr) {
      return false;
    }

    locationStr = String(locationStr) && locationStr.split(',');
    if ((locationStr.length < 3) || (locationStr.length > 4)) {
      return false;
    }

    const ret = {
      Lat: Number(locationStr[0]),
      Lon: Number(locationStr[1]),
      radius: Number(locationStr[2]),
      zoom: locationStr.length > 3 ? Number(locationStr[3]) : null,
    };

    // constrain the radius size to within bounds, and if it is invalid, set it to the default
    if (Number.isNaN(ret.radius)) {
      ret.radius = constants.SEARCH_RADIUS_DEFAULT_METERS;
    }

    // 10 Mile limit
    if (ret.radius < constants.SEARCH_RADIUS_MINIMUM_METERS) {
      ret.radius = constants.SEARCH_RADIUS_MINIMUM_METERS;
    }

    if (ret.radius > constants.SEARCH_RADIUS_MAXIMUM_METERS) {
      ret.radius = constants.SEARCH_RADIUS_MAXIMUM_METERS;
    }

    // Check min/max zoom
    if (ret.zoom
        && ((ret.zoom < app.leaflet.getMinZoom())
        || (ret.zoom > app.leaflet.getMaxZoom()))
    ) {
      return false;
    }

    // Validate if within USA bounds
    /*
    if (ret.Lat > app.leaflet.opts.maxBounds[0][0] &&
      ret.Lat < app.leaflet.opts.maxBounds[1][0] &&
      ret.Lon > app.leaflet.opts.maxBounds[0][1] &&
      ret.Lon < app.leaflet.opts.maxBounds[1][1])
*/
    return ret;

  //  return false;
  };

  Utils.inIFrame = function () {
    // iframe or facebook canvas
    return window.self !== window.top;
  };

  Utils.toLocationStr = function (Lat, Lon, Radius, Zoom) {
    const baseLocationStr = ''.concat(Number(Lat).toFixed(6), ',',
      Number(Lon).toFixed(6), ',',
      Number(Radius).toFixed(0));

    return Zoom ? baseLocationStr.concat(',', Zoom) : baseLocationStr;
  };

  Utils.convertToMiles = function (meters, decimals = 2) {
    meters = Number(meters || 0);
    return (meters * constants.MILES_PER_METER).toFixed(decimals);
  };

  Utils.formatSquareFeet = function (listing, showRange) {
    let squareFootage = '';
    if (showRange) {
      const low = listing.RangeLowSquareFeet ? Utils.addThousandSep(listing.RangeLowSquareFeet) : '';
      const high = listing.RangeHighSquareFeet ? Utils.addThousandSep(listing.RangeHighSquareFeet) : '';
      if ((!low) && (!high)) {
        return squareFootage;
      }
      squareFootage = `${low}-${high}`;
      return squareFootage;
    }

    squareFootage = listing.LivingSquareFeet ? Utils.addThousandSep(listing.LivingSquareFeet) : '';
    return squareFootage;
  };

  Utils.getBoundingBox = function (centerPoint, distanceLat, distanceLon) {
    /* eslint-disable */
    let MIN_LAT; let MAX_LAT; let MIN_LON; let MAX_LON; let R; let radDistLat; let radDistLon; let degLat; let degLon; let radLat; let radLon; let minLat; let maxLat; let minLon; let maxLon; let
      deltaLon;
    if (distanceLat < 0 || distanceLon < 0) {
      return 'Illegal arguments';
    }
    // helper functions (degrees<–>radians)
    const degToRad = function (deg) {
      return deg * (Math.PI / 180);
    };
    const radToDeg = function (rad) {
      return (180 * rad) / Math.PI;
    };
    // coordinate limits
    MIN_LAT = degToRad(-90);
    MAX_LAT = degToRad(90);
    MIN_LON = degToRad(-180);
    MAX_LON = degToRad(180);
    // Earth's radius (km)
    R = 6378.1;
    // angular distance in radians on a great circle
    radDistLat = distanceLat / R;
    radDistLon = distanceLon / R;
    // center point coordinates (deg)
    degLat = centerPoint[0];
    degLon = centerPoint[1];
    // center point coordinates (rad)
    radLat = degToRad(degLat);
    radLon = degToRad(degLon);
    // minimum and maximum latitudes for given distance
    minLat = radLat - radDistLat;
    maxLat = radLat + radDistLat;
    // minimum and maximum longitudes for given distance
    minLon = void 0;
    maxLon = void 0;
    // define deltaLon to help determine min and max longitudes
    deltaLon = Math.asin(Math.sin(radDistLon) / Math.cos(radLat));
    if (minLat > MIN_LAT && maxLat < MAX_LAT) {
      minLon = radLon - deltaLon;
      maxLon = radLon + deltaLon;
      if (minLon < MIN_LON) {
        minLon += 2 * Math.PI;
      }
      if (maxLon > MAX_LON) {
        maxLon -= 2 * Math.PI;
      }
    }
    // a pole is within the given distance
    else {
      minLat = Math.max(minLat, MIN_LAT);
      maxLat = Math.min(maxLat, MAX_LAT);
      minLon = MIN_LON;
      maxLon = MAX_LON;
    }
    return [
      radToDeg(minLon),
      radToDeg(minLat),
      radToDeg(maxLon),
      radToDeg(maxLat),
    ];
    /* eslint-enable */
  };

  /**
   * Create a diff object against current cursor state
   * set individual updates to the cursor
   *
   * @option: {forceKeys} make changes regardless
   * */
  Utils.updateCursor = function (opts) {
    const { cursor } = opts;
    const { defaults } = opts;
    let { finalState } = opts;
    const currentState = cursor.get();

    finalState = extend(true, {}, defaults, finalState);

    forEach(finalState, (i, key) => {
      if (i === null) {
        if (currentState[key] !== null) {
          // console.log('utils.updateCursor: nullKey:', key, i);
          cursor.set(key, null);
        }
      } else if (typeof i === 'object') {
        forEach(i, (inner, innerKey) => {
          if (!(typeof currentState[key] !== 'undefined'
            && currentState[key][innerKey] === inner)) {
            // console.log('utils.updateCursor: objectKey:', [key, innerKey], inner);
            cursor.set([key, innerKey], inner);
          }
        });
      } else if (opts.forceKeys
        || (!(typeof currentState !== 'undefined'
        && currentState[key] === i))) {
        // console.log('utils.updateCursor: singleKey:', key, i)
        cursor.set(key, i);
      }
    });
  };

  Utils.addThousandSep = function (num) {
    if (num && (typeof num === 'string' || typeof num === 'number')) {
      return (`${Number(num)}`).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, ($1) => `${$1},`);
    }
    return '';
  };

  Utils.addThousandSepIfNumber = function (num) {
    if (Utils.isNumeric(num)) {
      return Utils.addThousandSep(Utils.cleanNumber(num));
    }
    const cleanedUp = Utils.cleanNumber(num);
    if (Utils.isNumeric(cleanedUp)) {
      return Utils.addThousandSep(cleanedUp);
    }
    return num;
  };

  Utils.getNumberAbbr = function (num) {
    if (!Utils.isNumeric(num)) {
      return num;
    }

    const floatNumber = Number(Utils.cleanNumber(num));
    let output = (
      floatNumber < 1000
        ? `${floatNumber}`
        : floatNumber < 1000000
          ? `${(floatNumber / 1000).toFixed()}K`
          : `${(floatNumber / 10000).toFixed(2)}M`);

    output = String(output).replace('.00M', 'M').replace('0M', 'M');
    return output;
  };

  Utils.getNumberAbbrIfNumber = function (num) {
    if (Utils.isNumeric(num)) {
      return Utils.getNumberAbbr(num);
    }
    const cleanedUp = Utils.cleanNumber(num);
    if (Utils.isNumeric(cleanedUp)) {
      return Utils.getNumberAbbr(cleanedUp);
    }
    return num;
  };

  Utils.getNumberFromString = function (numStr) {
    const num = Utils.cleanNumber(numStr);
    if (Utils.isNumeric(num)) {
      return Number(Utils.cleanNumber(num));
    }
    return numStr;
  };

  Utils.cleanNumber = function (num) {
    return String(num).replace(/,/g, '').replace(/\+/g, '').replace(/\$/g, '');
  };

  Utils.isNumeric = function (n) {
    const cleanedNum = Utils.cleanNumber(n);
    return cleanedNum !== '0' && (cleanedNum).match(/^\d*(\.\d+)?$/);
  };

  Utils.isPhoneSizedScreen = function () {
    // 580px was chosen as a rough estimate of the width that the map screen starts to become less functional
    return window.matchMedia && (window.matchMedia('(max-width: 580px)').matches || window.matchMedia('(max-height: 580px)').matches);
  };

  Utils.isMobile = function () {
    if (Utils.clientIsMobile) {
      return Utils.clientIsMobile;
    }

    const isMobile = {
      Android() {
        return navigator.userAgent.match(/Android/i);
      },
      BlackBerry() {
        return navigator.userAgent.match(/BlackBerry/i);
      },
      iOS() {
        return navigator.userAgent.match(/iPhone|iPad|iPod/i);
      },
      Opera() {
        return navigator.userAgent.match(/Opera Mini/i);
      },
      Windows() {
        return navigator.userAgent.match(/IEMobile/i);
      },
      any() {
        return (isMobile.iOS() || isMobile.Android() || isMobile.Windows()
        || isMobile.BlackBerry() || isMobile.Opera());
      },
    };

    Utils.clientIsMobile = isMobile.any() != null;

    return Utils.clientIsMobile;
  };

  Utils.isIos = function () {
    return navigator.userAgent.match(/iPhone|iPad|iPod/i);
  };

  Utils.isSafariBrowser = function () {
    return (navigator.userAgent.match(/Safari/i) && !navigator.userAgent.match(/Chrome/i));
  };

  Utils.toTitleCase = function (str) {
    return (str || '').replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
  };

  // Google Streetview Unavailable Image
  Utils.streetViewNotAvailableImage = function () {
    return 'https://nplayassets.blob.core.windows.net/search2/no-photo-v2.jpg';
  };

  Utils.useMobileSite = function () {
    return app.actions.common.forceMobile || (Utils.isMobile() && Utils.isPhoneSizedScreen());
  };

  // Geolocation

  Utils.geolocationHelperOneTimeCallbacks = [];
  Utils.geolocationHelperCallbacks = [];
  Utils.geolocationLoaded = false;
  Utils.lastPosition = null;

  Utils.geolocationSuccess = function (position) {
    app.actions.analytics.sendEvent('search', 'current location', 'success');

    Utils.geolocationLoaded = true;

    Utils.lastPosition = position;

    forEach(Utils.geolocationHelperCallbacks, (func) => {
      func(null, position);
    });

    forEach(Utils.geolocationHelperOneTimeCallbacks, (func) => {
      func(null, position);
    });

    delete Utils.geolocationHelperOneTimeCallbacks;
    Utils.geolocationHelperOneTimeCallbacks = [];
  };

  Utils.geolocationError = function (error) {
    Utils.geolocationLoaded = false;

    alert('Uh-oh! Looks like your device doesn’t support location services or your have it turned off. You can modify this in your browser settings.');

    forEach(Utils.geolocationHelperCallbacks, (func) => {
      func('Geolocation failed');
    });

    forEach(Utils.geolocationHelperOneTimeCallbacks, (func) => {
      func('Geolocation failed');
    });

    delete Utils.geolocationHelperOneTimeCallbacks;
    Utils.geolocationHelperOneTimeCallbacks = [];

    if (error == 'Not supported') {
      console.warn('Geolocation not supported.');

      app.actions.analytics.sendEvent('search', 'current location', 'error not supported');
      return;
    }
    switch (error.code) {
      case error.PERMISSION_DENIED:
        console.warn('User denied the request for Geolocation.');
        app.actions.analytics.sendEvent('search', 'current location', 'error user denied');
        break;
      case error.POSITION_UNAVAILABLE:
        console.warn('Location information is unavailable.');
        app.actions.analytics.sendEvent('search', 'current location', 'error position unavailable');
        break;
      case error.TIMEOUT:
        console.warn('The request to get user location timed out.');
        app.actions.analytics.sendEvent('search', 'current location', 'error time out');
        break;
      case error.UNKNOWN_ERR:
        console.warn('An unknown error occurred.');
        app.actions.analytics.sendEvent('search', 'current location', 'error unknown');
        break;
      default:
        console.warn('An unknown error occurred.');
        app.actions.analytics.sendEvent('search', 'current location', 'error unknown');
        break;
    }
  };

  Utils.GeolocationWatch = function () {
    if (Utils.geolocationLoaded) {
      return;
    }

    if (navigator.geolocation) {
      Utils.geolocationLoaded = true;
      app.actions.analytics.sendEvent('search', 'current location', 'initiated');

      app.leaflet.watchCurrentLocation();
      navigator.geolocation.watchPosition(
        Utils.geolocationSuccess,
        Utils.geolocationError,
        {
          timeout: 20000,
          maximumAge: Infinity,
        },
      );
    } else {
      app.actions.analytics.sendEvent('search', 'current location', 'unsupported');
      Utils.geolocationError('Not supported');
    }
  };

  Utils.formatPhone = function (phone) {
    if (!phone) {
      return 'Not Available';
    }
    let number = phone;
    number = number.replace(/[^0-9]/g, '');

    if (number.length !== 10) {
      return phone;
    }
    number = number.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    return number;
  };

  Utils.formatPriceLabel = function (_propertyValue, o) {
    if (!_propertyValue) {
      return undefined;
    }

    let labelSuffix = '';
    let propertyValue = _propertyValue;
    if (o.Listing && o.Listing.SaleType === 1) {
      const monthlyPrice = Utils.getMortgagePayment(o.Listing);
      if (monthlyPrice) {
        if (monthlyPrice < 0) {
          return undefined;
        }
        propertyValue = monthlyPrice;
        labelSuffix = '/mo';
        return Utils.addThousandSep(propertyValue) + labelSuffix;
      }
    }

    let priceLabel = `${propertyValue < 1000 ? propertyValue
      : propertyValue < 100000 ? `${(propertyValue / 1000).toFixed(1)}K`
        : propertyValue < 1000000 ? `${(propertyValue / 1000).toFixed()}K`
          : `${(propertyValue / 1000000).toFixed(2)}M`}`;

    priceLabel = priceLabel.replace('.00M', 'M').replace('0M', 'M');

    return priceLabel + labelSuffix;
  };

  Utils.getPropertyTaxRate = function (listing) {
    if (!listing) {
      return 0;
    }

    let state = listing.Address && listing.Address.State || listing.State || '';
    state = state.toUpperCase();

    const countyCode = listing.CountyCode;

    return propertyTaxByCounty[
      ''.concat(
        (countyCode || '')
          .replace(/(,\s|-)[A-Z]{2}$/, '')
          .toUpperCase()
          .replace('ST ', 'SAINT ')
          .replace('ST. ', 'SAINT ')
          .replace(' COUNTY', '')
          .replace(' Municipality'.toUpperCase(), '')
          .replace(' Borough'.toUpperCase(), ''),
        ', ',
        stateLongToShort[state] || state,
      ).toUpperCase()
    ] || 1.5;
  };

  Utils.getMortgagePayment = function (listing) {
    if (listing && listing.SaleType === 1 && listing.MortgagePaymentInfo && listing.MortgagePaymentInfo.TotalPayment) {
      return Math.round(listing.MortgagePaymentInfo.TotalPayment / 10) * 10;
    }
  };

  Utils.getDollarSymbol = function (listing) {
    if ((listing && listing.MlsIds || []).includes('ontrreb')) {
      return 'C$';
    }
    return '$';
  };

  Utils.makeUrlsClickable = function (text) {
    if (text) {
      let output = text.replace(/<[^>]+>/g, '');
      const markdownUrlPattern = /\[([^\]]+)\]\((\b(https?|ftp):\/\/[-A-Z0-9+&@#/%?=~_|!:,.;]*[-A-Z0-9+&@#/%=~_|])\)/gim;
      output = output.replace(markdownUrlPattern, '<a href="$2" target="_blank">$1</a>');
      // Make sure it's not already a link converted from markdown
      const urlPattern = /([\s^])(\b(https?|ftp):\/\/[-A-Z0-9+&@#/%?=~_|!:,.;]*[-A-Z0-9+&@#/%=~_|])/gim;
      return output.replace(urlPattern, '$1<a href="$2" target="_blank">$2</a>');
    }
    return '';
  };

  Utils.removeQs = function () {
    const uri = window.location.toString();
    if (uri.indexOf('?') > 0) {
      const cleanUri = uri.substring(0, uri.indexOf('?'));
      window.history.replaceState({}, document.title, cleanUri);
    }
  };

  Utils.uniqueMLSIdsFromListings = function (listings) {
    const mlsIds = [];
    forEach(listings, (listing) => {
      forEach(listing && listing.MlsIds || [], (mlsId) => {
        if (mlsId && mlsIds.indexOf(mlsId) === -1) {
          mlsIds.push(mlsId);
        }
      });
    });
    return mlsIds;
  };

  Utils.removeQueryString = function () {
    const cleanUri = `${location.protocol}//${location.host}${location.pathname}`;
    /*
     var hash_pos = location.href.indexOf("#");
     if (hash_pos > 0) {
     var hash = location.href.substring(hash_pos, location.href.length);
     clean_uri += hash;
     }
     */
    window.history.replaceState({}, document.title, cleanUri);
  };

  Utils.objToQs = function (dataObj) {
    // No Question Mark
    let qs = '';
    if (typeof dataObj === 'object') {
      qs = map(dataObj, (val, k) => {
        if (!val) {
          return '';
        }
        if (typeof val !== 'object') {
          return `${encodeURIComponent(k)}=${encodeURIComponent(val)}`;
        }
        let query = `${encodeURIComponent(k)}[]=${encodeURIComponent(val[0])}`;
        for (let i = 1; i < val.length; i++) {
          query += `&${encodeURIComponent(k)}[]=${encodeURIComponent(val[i])}`;
        }
        return query;
      }).join('&').replace(/&+/g, '&').replace(/&$/, '');
    }
    return qs;
  };

  Utils.getMonthlyPrincipalAndInterest = function (pricipal, periods, monthlyInterestRate) {
    try {
      return pricipal * monthlyInterestRate * (Math.pow(1 + monthlyInterestRate, periods)) / (Math.pow(1 + monthlyInterestRate, periods) - 1);
    } catch (ex) {
      return 0;
    }
  };

  Utils.calculateMortgagePayment = function (options) {
    try {
      if (!options.years) {
        return null;
      }

      const homeSellingPrice = options.sellingPrice;
      let downPaymentPercentage = options.downPaymentPercentage / 100;
      if (downPaymentPercentage > 1 || downPaymentPercentage < 0) {
        downPaymentPercentage = null;
      }
      const { downPaymentAmount } = options;

      const principle = homeSellingPrice
        - (downPaymentPercentage ? homeSellingPrice * downPaymentPercentage : downPaymentAmount);

      const annualInterestRate = options.interestRate;
      const monthlyInterestRate = annualInterestRate / 100 / 12;

      const periods = options.years * 12;

      const monthlyLoanPayment = Utils.getMonthlyPrincipalAndInterest(principle, periods, monthlyInterestRate);

      let monthlyPayment = monthlyLoanPayment;

      let propertyTaxPercentage = options.propertyTaxPercentage / 100;
      if (propertyTaxPercentage > 1 || propertyTaxPercentage < 0) {
        propertyTaxPercentage = null;
      }
      const propertyTaxAmount = options.propertyTaxAmount ? options.propertyTaxAmount : homeSellingPrice * propertyTaxPercentage;

      monthlyPayment += propertyTaxAmount / 12;

      if (options.homeInsurance) {
        monthlyPayment += options.homeInsurance / 12;
      }

      if (options.includePMI) {
        if (typeof options.includePMI === 'number') {
          monthlyPayment += options.includePMI;
        } else {
          monthlyPayment += principle * 0.01 / 12; // Assuming 1%
        }
      }

      if (options.hoa) {
        monthlyPayment += options.hoa;
      }

      return Math.round(monthlyPayment);
    } catch (ex) {
      return null;
    }
  };

  Utils.geocodeAddress = function (address, cb, key) {
    app.api.geocodeAddress(address, (res) => {
      if (res && typeof res === 'object') {
        cb(res);
      } else {
        cb(null);
      }
    }, key);
  };

  Utils.geocodeLatlng = function (lat, lng, cb) {
    app.api.geocodeLatLng(lat, lng, (res) => {
      if (res && typeof res === 'object') {
        cb(res);
      } else {
        cb(null);
      }
    });
  };

  Utils.getMortgageInfo = function ({
    state, county, propertyType, listPrice, isUSDA,
  }, cb) {
    app.api.getMortgageInfo({
      state, county, propertyType, listPrice, isUSDA,
    }, (err, res) => {
      if (res && typeof res === 'object') {
        cb(res);
      } else {
        cb(null);
      }
    });
  };

  Utils.getRateplugLOInfo = function (loInfo, cb) {
    app.api.getRateplugLOInfo(loInfo, (err, res) => {
      if (res && typeof res === 'object') {
        cb(res);
      } else {
        cb(null);
      }
    });
  };

  Utils.getRateplugPropertyDetails = function ({
    // eslint-disable-next-line camelcase
    FIPS, FC_PropertyID, AS_PropertyID, FHAEligible, VAEligible, USDAEligible, ListPrice, PropertyType, DownPayment,
  }, cb) {
    app.api.getRateplugPropertyDetails({
    // eslint-disable-next-line camelcase
      FIPS, FC_PropertyID, AS_PropertyID, FHAEligible, VAEligible, USDAEligible, ListPrice, PropertyType, DownPayment,
    }, (err, res) => {
      if (res && typeof res === 'object') {
        cb(res);
      } else {
        cb(null);
      }
    });
  };

  Utils.postRatePlugArchive = function (body, cb = () => { }) {
    app.api.postRatePlugArchive(body, (err, res) => {
      if (res && typeof res === 'object') {
        cb(res);
      } else {
        cb(null);
      }
    });
  };

  Utils.sendLOMessage = function (body, cb = () => { }) {
    app.api.sendLOMessage(body, (err, res) => {
      if (res && !err) {
        cb(res);
      } else {
        cb({ ErrorMessage: 'Failed to send message. Please reach the loan officer directly using the contact information above.' });
      }
    });
  };

  Utils.propertyTypeStringToRatePlugPropertyType = function (propertyTypeString) {
    switch (propertyTypeString) {
      case 'MultiFamily':
        return 'Multi-Family';
      case 'TownHomeCondo':
        return 'Condo';
      case 'LotsLand':
        return 'Land';
      default:
        break;
    }

    return 'SingleFamily';
  };

  Utils.webGlSupported = (function () {
    try {
      const canvas = document.createElement('canvas');
      return !!window.WebGLRenderingContext && !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch (e) {
      return false;
    }
  }());

  Utils.associationPeriodToText = function (period) {
    switch (period) {
      case 'Annual':
      case 'Annually':
        return '/yr';
      case 'Quarterly':
        return '/3mo';
      case 'Monthly':
        return '/mo';
      // if blank, don't list a unit
      default:
        break;
    }

    switch (Number(period)) {
      case 1:
        return '/yr';
      case 2:
        return '/6mo';
      case 4:
        return '/3mo';
      case 12:
        return '/mo';
      // if blank, don't list a unit
      default:
        break;
    }

    return '';
  };

  Utils.getAgentSettingValue = function (agentSettings, key) {
    return ((agentSettings || []).find((s) => s.Key === key) || {}).Value;
  };

  Utils.getListingPixelAttributes = function (listing) {
    if (!listing) {
      return {};
    }
    try {
      return {
        content_type: 'home_listing',
        content_ids: [listing.Id],
        city: listing.CityName,
        region: listing.State,
        country: 'US',
        currency: 'USD',
        availability: listing.Status.match(/pending/i) ? 'sale_pending'
          : listing.SaleType === 2 ? 'for_sale'
            : listing.SaleType === 1 ? 'for_rent'
              : 'off_market',
        listing_type: listing.SaleType === 2 ? 'for_rent_by_agent' : 'for_sale_by_agent',
        neighborhood: listing.Neighborhood,
        preferred_baths_range: listing.Rooms && listing.Rooms.TotalBaths && [listing.Rooms.TotalBaths, listing.Rooms.TotalBaths] || undefined,
        preferred_beds_range: listing.Rooms && listing.Rooms.TotalBedrooms && [listing.Rooms.TotalBedrooms, listing.Rooms.TotalBedrooms] || undefined,
        preferred_price_range: listing.ListPrice && [listing.ListPrice, listing.ListPrice],
      };
    } catch (_) {
      return {};
    }
  };

  return Utils;
};
