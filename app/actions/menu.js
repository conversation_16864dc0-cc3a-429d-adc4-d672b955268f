const forEach = require('lodash.foreach');
const querystring = require('querystring');

module.exports = function (app) {
  /** *
   * Static methods for Panels
   * */
  function Menu() {
  }

  Menu.layoutCursor = app.cursor.select('layout');
  Menu.sharedCursor = app.cursor.select('shared');
  Menu.sharedMenuCursor = app.cursor.select('shared', 'menu');

  Menu.route = function (route) {
    Menu.toggle(true);

    if (Menu.resumeObject) {
      Menu.resumeSearchHistory(
        Menu.resumeObject.agentId,
        Menu.resumeObject.searchText,
        Menu.resumeObject.searchLocationStr,
        Menu.resumeObject.filters,
        Menu.resumeObject.searchDate,
        Menu.resumeObject.theme,
        Menu.resumeObject.rateplugBuyerId,
      );
      Menu.resumeObject = null;
    }

    if (!route.lastUrl) {
      return; // app.actions.common.goToRoute('')
    }

    Menu.setBackRoute(route);
  };
  Menu.resumeObject = null;

  Menu.backRoute = null;
  Menu.setBackRoute = function (route) {
    if (route.lastUrl
      && route.lastUrl.indexOf('/agent') === -1
      && route.lastUrl.indexOf('/buyer') === -1
      && route.lastUrl.indexOf('/menu') === -1
    ) {
      Menu.backRoute = route.lastUrl;
    }
  };

  Menu.setPageTitle = function () {
    const listingCount = app.cursor.facets.listingCount.get();
    if (listingCount) {
      app.actions.common.setPageTitle(''.concat(listingCount, ' Search Results'));
    } else {
      app.actions.common.resetPageTitle();
    }
  };

  Menu.toggle = function (needsToOpen) {
    const menuNeedsToBeOpened = needsToOpen || !Menu.layoutCursor.select('menu').get();
    const inGrid = window.location.href.indexOf('/grid') !== -1;

    if (menuNeedsToBeOpened) {
      if (inGrid) {
        app.actions.grid.currentRoute ? Menu.backRoute = app.actions.grid.currentRoute.url : '';
      } else {
        app.actions.map.currentRoute ? Menu.backRoute = app.actions.map.currentRoute.url : '';
      }

      Menu.setPageTitle();

      if (window.location.href.indexOf('/menu/') === -1) { // No need to push route if coming in directly
        app.actions.common.goToRoute('/menu/'.concat(inGrid ? 'grid' : 'map'), {
          skip: true,
        });
      }

      app.utils.updateCursor({
        cursor: Menu.layoutCursor,
        defaults: app.cursorDefaults.layout,
        finalState: { menu: menuNeedsToBeOpened },
      });

      const locationStr = ''.concat(app.cursor.get(['panels', 'listings', 'meta', 'locationStr']));
      const pos = app.utils.validateLocationStr(locationStr);
      if (pos && !app.actions.common.getLocationQuery() && !app.actions.common.isLocationQueryPlaceholder()) {
        app.actions.common.saveLocationQuery('Current location based on map coordinates');
      }

      app.cursor.commit();

      app.actions.analytics.sendEvent('navigation', 'search menu');
    } else if (inGrid) {
      app.actions.grid.onNav();
    } else {
      app.actions.map.onNav();
    }
  };

  Menu.toggleSortType = function (type) {
    const currSortType = Menu.sharedMenuCursor.get('sortType');
    Menu.sharedMenuCursor.set('sortType', currSortType === type ? null : type);
    app.cursor.commit();

    Menu.updateUrlWithSearchParams();
    app.actions.panels.refreshListings();

    if (currSortType !== type) {
      app.actions.analytics.sendEvent('sort', type, window.location.href.indexOf('/grid') !== -1 ? 'grid' : 'map');
    }
  };

  Menu.getMenuSelections = function () {
    return Menu.sharedMenuCursor.get();
  };

  Menu.resetMenuSelections = function () {
    app.utils.updateCursor({
      cursor: Menu.sharedMenuCursor,
      defaults: app.cursorDefaults.shared.menu,
      finalState: { homeTypesArray: null, keywords: null, specialFinancing: null },
    });
    Menu.sharedMenuCursor.set('homeTypesArray', []);
    Menu.sharedMenuCursor.set('keywords', []);
    Menu.sharedMenuCursor.set('specialFinancing', []);
    app.cursor.commit();

    // Resetting the bounding box will force a full refresh on desktop
    //   On phone-sized screens, it will have no effect
    app.leaflet.resetBoundingBox();

    // Reload listings with new menu options
    app.actions.panels.refreshListings();
  };

  Menu.setMlsFilter = function (mlsId) {
    Menu.sharedMenuCursor.set('mlsId', mlsId);
    app.cursor.commit();
  };

  Menu.updateSearchField = function (items, opts = { skipListingRefresh: false }) {
    for (let i = 0; i < items.length; i++) {
      const fieldName = items[i].key;
      const fieldValue = items[i].value;
      if (typeof fieldName !== 'undefined' && typeof fieldValue !== 'undefined') {
        Menu.sharedMenuCursor.set(fieldName, fieldValue);
      }
    }
    app.cursor.commit();

    // Resetting the bounding box will force a full refresh on desktop
    //   On phone-sized screens, it will have no effect
    app.leaflet.resetBoundingBox();

    if (app.router.currentlyOnRoute(app.router.ROUTE_NAMES.MAP) || app.router.currentlyOnRoute(app.router.ROUTE_NAMES.GRID)) {
      Menu.updateUrlWithSearchParams();
    }

    if (!opts.skipListingRefresh) {
      // Reload listings with new menu options
      app.actions.panels.refreshListings();
    }
  };

  Menu.updateUrlWithSearchParams = function () {
    const queryParams = Menu.sharedMenuCursor.get() || {};
    if (!queryParams.sortType) {
      queryParams.sortType = 'daysOnMarket_asc';
    }
    const url = app.router.generateUrl(app.router.currentRoute.name, app.router.currentRoute.params, queryParams, { preserveQuery: true });
    app.router.go(url, { replace: true, skip: true });
  };

  Menu.updateSearchSelections = function (newSelections) {
    app.utils.updateCursor({
      cursor: Menu.sharedMenuCursor,
      defaults: app.cursorDefaults.shared.menu,
      finalState: newSelections,
    });

    app.cursor.commit();

    // Resetting the bounding box will force a full refresh on desktop
    //   On phone-sized screens, it will have no effect
    app.leaflet.resetBoundingBox();

    // Reload listings with new menu options
    app.actions.panels.refreshListings();
  };

  Menu.getSearchSelectionsOpts = function () {
    const menuSelections = Menu.getMenuSelections();
    // const agentData = Menu.sharedCursor.get(['agent', 'data']);

    return {
      mlsId: menuSelections.mlsId,
      // mlsAgentId: agentData.MlsAgentId,
      sortType: menuSelections.sortType ? menuSelections.sortType.toUpperCase() : null,
      saleType: menuSelections.saleType,
      maxPrice: menuSelections.maxPrice,
      minPrice: menuSelections.minPrice,
      maxMortgagePayment: menuSelections.maxMortgagePayment,
      minMortgagePayment: menuSelections.minMortgagePayment,
      downPayment: menuSelections.downPayment,
      minBeds: menuSelections.minBeds,
      minBaths: menuSelections.minBaths,
      minYear: menuSelections.minYear,
      maxYear: menuSelections.maxYear,
      minLotSizeSqft: menuSelections.minLotSizeSqft,
      maxLotSizeSqft: menuSelections.maxLotSizeSqft,
      minLivingSqft: menuSelections.minLivingSqft,
      maxLivingSqft: menuSelections.maxLivingSqft,
      minGarage: menuSelections.minGarage,
      maxGarage: menuSelections.maxGarage,
      minStories: menuSelections.minStories,
      maxStories: menuSelections.maxStories,
      openHouse: menuSelections.openHouse,
      keywords: menuSelections.keywords,
      statusEq: menuSelections.statusEq,
      homeTypesArray: menuSelections.homeTypesArray,
      specialFinancing: menuSelections.specialFinancing,
    };
  };

  Menu.updateSearchHistory = function (delay, cb) {
    if (!app.actions.common.isLoggedIn()) {
      app.cursor.set(['panels', 'previousSearches', 'data'], false);
      app.cursor.commit();
      return;
    }
    app.cursor.set(['panels', 'previousSearches', 'data'], null);
    app.cursor.commit();
    app.api.searchHistory(delay, (res) => {
      if (res && typeof res === 'object') {
        app.cursor.set(['panels', 'previousSearches', 'data'], res);
        app.cursor.commit();

        if (cb) {
          cb(res);
        }
      } else {
        app.cursor.set(['panels', 'previousSearches', 'data'], false);
        app.cursor.commit();

        if (cb) {
          cb(false);
        }
      }
    });
  };

  Menu.seeMoreHomesLikeThis = function (listing) {
    if (listing) {
      Menu.sharedMenuCursor.set('minPrice', parseInt(listing.ListPrice * 0.90, 10));
      Menu.sharedMenuCursor.set('maxPrice', parseInt(listing.ListPrice * 1.10, 10));
      Menu.sharedMenuCursor.set('minBaths', listing.TotalBaths);
      Menu.sharedMenuCursor.set('minBeds', listing.TotalBedrooms);
      Menu.sharedMenuCursor.set('sortType', 'DISTANCE_ASC'); // DISTANCE_ASC

      let locationStrArr = [];
      const locationStr = app.actions.map.listingsCursor.get('meta', 'locationStr');
      if (typeof locationStr === 'string') {
        locationStrArr = locationStr.split(',');
      }
      locationStrArr[0] = listing.Location.Lat;
      locationStrArr[1] = listing.Location.Lon;
      locationStrArr[2] = locationStrArr[2] || app.leaflet.opts.circleDefaultRadius;

      const route = '/search/map/'.concat(locationStrArr.join(','));
      app.actions.common.goToRoute(route);
    }
  };

  Menu.resumeSearchHistory = function (agentId, searchText, searchLocationStr, filters, searchDate, theme, rateplugBuyerId) {
    // Always use Days on market for resuming search history so that new listings are shown up top
    filters = filters || {};
    filters.st = 13; // Days on Market ASC
    // filters.specialFinancing = Menu.getMenuSelections().specialFinancing; // Persist selected special financing option

    if (theme) {
      window.sessionStorageAlias.setItem('HA_THEME', theme);
    } else {
      window.sessionStorageAlias.removeItem('HA_THEME');
    }

    const inGrid = window.location.href.indexOf('/grid') !== -1;
    theme = theme || undefined;

    if (rateplugBuyerId) {
      const url = `/${agentId}/search/${inGrid ? 'grid' : 'map'}/${searchLocationStr}?${
        querystring.stringify({
          ...filters,
          rp_buyer: rateplugBuyerId,
          q: searchText,
          theme,
        })
      }`;

      window.location.href = url;
      return;
    }

    if ((document.body.dataset.theme || '') !== (theme || '')) {
      const url = `/${agentId}/search/${inGrid ? 'grid' : 'map'}/${searchLocationStr}?${
        querystring.stringify({
          ...filters,
          q: searchText,
          theme,
        })
      }`;

      window.location.href = url;
      return;
    }

    if (agentId != app.actions.common.getAgentId()) {
      // Switch Agent
      Menu.resumeObject = {
        agentId,
        searchText,
        searchLocationStr,
        filters,
        searchDate,
        theme,
        rateplugBuyerId,
      };
      app.router.go('/'.concat(agentId, '/menu/', inGrid ? 'grid' : 'map'));
      return;
    }

    Menu.sharedCursor.set('savedSearch', {
      agentId,
      searchText,
      searchLocationStr,
      filters,
      searchDate,
      theme,
      rateplugBuyerId,
    });

    app.cursor.set(['panels', 'listings', 'meta', 'locationStr'], searchLocationStr);
    app.actions.common.saveLocationQuery(searchText);

    app.utils.updateCursor({
      cursor: Menu.sharedMenuCursor,
      defaults: app.cursorDefaults.shared.menu,
      finalState: { homeTypesArray: null, keywords: null, specialFinancing: null },
    });
    Menu.sharedMenuCursor.set('homeTypesArray', []);
    Menu.sharedMenuCursor.set('keywords', []);
    Menu.sharedMenuCursor.set('specialFinancing', []);

    forEach(Menu.sharedMenuCursor.get(), (value, key) => {
      if (filters[key]) {
        Menu.sharedMenuCursor.set(key, filters[key]);
      }
    });

    const route = '/search/map/'.concat(searchLocationStr);
    app.actions.common.goToRoute(route, { replace: true });
  };

  Menu.searchCurrentLocation = function (saleType, cb = () => {}) {
    app.actions.common.flagUserAsInteractedWithSite();

    app.actions.analytics.sendEvent('search', 'current location', 'started');

    const callback = function (err, position) {
      if (err) {
        return cb(err);
      }
      const location = { lat: position.coords.latitude, lon: position.coords.longitude };

      app.actions.analytics.sendEvent('search', 'current location', 'success');

      Menu.reverseGeocodeCoord(location, (res) => {
        if (res) {
          app.actions.common.saveLocationQuery('', true);

          const inMemberListings = window.location.href.indexOf('/member-listings') !== -1;
          if (inMemberListings) {
            const locationStr = ''.concat(location.lat, ',', location.lon, ',', app.leaflet.opts.circleDefaultRadius);
            const route = `member-listings/${locationStr}`;
            app.router.go(route);
          } else if (app.actions.common.getAgentId()) {
            // if we have an agent, go to landing onNav
            app.actions.landing.onSearch(location.lat, location.lon, null,
              res.formatted_address, saleType || 1, {
                currentLocation: true,
              });
          } else {
            // if we need an agent, use home onNav
            app.actions.home.onNav(location.lat, location.lon, null,
              res.formatted_address, saleType || 1, {
                currentLocation: true,
              });
          }
          cb();
        } else {
          cb('Geolocation failed.');
        }
      });
    };

    if (app.utils.lastPosition) {
      callback(null, app.utils.lastPosition);
    } else {
      app.utils.geolocationHelperOneTimeCallbacks.push(callback);
      app.utils.GeolocationWatch();
    }
  };

  Menu.agentSearchCurrentLocation = function (cb = () => {}) {
    app.actions.common.flagUserAsInteractedWithSite();

    app.actions.analytics.sendEvent('search', 'current location', 'started');

    const callback = function (err, position) {
      if (err) {
        return cb(err);
      }

      const location = { lat: position.coords.latitude, lon: position.coords.longitude };

      app.actions.analytics.sendEvent('search', 'current location', 'success');

      app.router.go(app.router.generateUrl(app.router.ROUTE_NAMES.AGENT_SEARCH, {
        location: `${location.lat},${location.lon},${6200}`,
      }));

      cb();
    };

    if (app.utils.lastPosition) {
      callback(null, app.utils.lastPosition);
    } else {
      app.utils.geolocationHelperOneTimeCallbacks.push(callback);
      app.utils.GeolocationWatch();
    }
  };

  Menu.onAutosuggest = {

    showWhen(val) {
      return val && (val.trim().length > 2 || val.trim().length === 0);
    },

    suggestionValue(data) {
      return data.value;
    },

    nlpIdentified: false,
    nlpLocationIdentified: false,

    api_noAddresses(val, cb) {
      Menu.onAutosuggest.api(val, cb, ['A']);
    },

    api_more(val, cb) {
      Menu.onAutosuggest.api(val, cb, ['A'], true);
    },

    api(val, cb, locTypeFilter, noCurrentLocation) {
      Menu.onAutosuggest.nlpIdentified = false;
      Menu.onAutosuggest.nlpLocationIdentified = false;
      Menu.onAutosuggest.nlpLatestQuery = val;
      const query = val.trim();
      if (query.length > 2) {
        const autoSuggestPromise = new Promise((resolve) => {
          app.api.autosuggest(query, (res) => {
            /* Skip NLP for now
            if ((res && typeof res === 'object' && res.length === 0) || res === null) {
              // Check NLP Server
              app.api.nlp(val, (res) => {
                if (res && typeof res === 'object' && typeof res.result === 'object'
                  && res.query === Menu.onAutosuggest.nlpLatestQuery) {
                  const updates = [];
                  let shouldShowFilters = false;

                  if (res.result.saleType) {
                    updates.push({
                      key: 'saleType',
                      value: res.result.saleType === 'rent' ? 2 : 1,
                    });
                  }
                  if (res.result.bed) {
                    updates.push({
                      key: 'minBeds',
                      value: res.result.bed > 5 ? 5 : res.result.bed,
                    });
                    shouldShowFilters = true;
                  }
                  if (res.result.bath) {
                    updates.push({
                      key: 'minBaths',
                      value: res.result.bath > 4 ? 4 : res.result.bath,
                    });
                    shouldShowFilters = true;
                  }
                  if (res.result.maxPrice) {
                    updates.push({
                      key: 'maxPrice',
                      value: res.result.maxPrice,
                    });
                    shouldShowFilters = true;
                  }
                  if (res.result.minPrice) {
                    updates.push({
                      key: 'minPrice',
                      value: res.result.minPrice,
                    });
                    shouldShowFilters = true;
                  }
                  if (res.result.minSqft) {
                    updates.push({
                      key: 'minLivingSqft',
                      value: res.result.minSqft,
                    });
                    shouldShowFilters = true;
                  }
                  if (res.result.maxSqft) {
                    updates.push({
                      key: 'maxLivingSqft',
                      value: res.result.maxSqft,
                    });
                    shouldShowFilters = true;
                  }
                  if (res.result.type) {
                    if (res.result.type === 'house') {
                      updates.push({
                        key: 'homeTypesArray',
                        value: [1],
                      });
                    } else if (res.result.type === 'condo' || res.result.type === 'apartment') {
                      updates.push({
                        key: 'homeTypesArray',
                        value: [3],
                      });
                    }
                  }
                  if (res.result.location && res.result.location.coordinates) {
                    Menu.onAutosuggest.onNav(
                      res.result.location.coordinates.lat,
                      res.result.location.coordinates.lng,
                      app.leaflet.opts.circleDefaultRadius,
                    );
                    Menu.onAutosuggest.nlpIdentified = true;
                    Menu.onAutosuggest.nlpLocationIdentified = true;
                  }

                  if (updates.length > 0) {
                    Menu.updateSearchField(updates);
                    Menu.onAutosuggest.nlpIdentified = true;

                    if (shouldShowFilters) {
                      Menu.setEditingFilters(true);
                    }
                  }
                }
              });
            }
            */

            res && typeof res === 'object'
              ? resolve(res)
              : resolve([]);
          },
          locTypeFilter);
        });

        const mlsListingIdPromise = new Promise((resolve) => {
          const agentData = app.actions.common.getAgentData();
          const mlsId = agentData && agentData.MlsId;
          if (!mlsId) {
            return resolve([]);
          }
          app.api.listingsRaw({ ct: 1, mlsId, mlsListingId: query }, (res) => {
            if (res && typeof res === 'object') {
              resolve(res.map((l) => ({
                type: 'mlsListingIdMatch',
                value: `${l.FullStreetAddress}, ${l.CityName}, ${l.State} ${l.ZipCode}`,
                location: {
                  locType: 'A',
                  lat: l.Location.Lat,
                  lon: l.Location.Lon,
                  locId: l.Id,
                },
              })));
            } else {
              resolve([]);
            }
          });
        });

        Promise.all([mlsListingIdPromise, autoSuggestPromise])
          .then((results) => {
            cb(null, [...results[0] || [], ...results[1] || []]);
          });
      } else if (val.trim().length === 0) {
        const suggestions = noCurrentLocation ? [] : [{ value: 'Current Location' }];
        const loadSearchHistory = window.location.href.indexOf('/member-listings') === -1;
        if (app.actions.common.isLoggedIn() && loadSearchHistory) {
          Menu.updateSearchHistory(0, (res) => {
            if (res) {
              forEach(res, (val1) => {
                suggestions.push({
                  searchHistory: val1,
                  value: val1.CustomName || val1.SearchText,
                });
              });
              cb(null, suggestions);
            }
          });
        } else {
          cb(null, suggestions);
        }
      } else {
        cb(null, null);
      }
    },

    onNav(Lat, Lon, radius, query, opts = {}) {
      console.log('-- Suggestion value --', Lat, Lon);

      if (query) {
        window.localStorageAlias.setItem('LAST_SEARCH_TERM', JSON.stringify({
          lat: Lat,
          lon: Lon,
          radius,
          locationQuery: query,
        }));
      }

      /* // NPLAY-5240 Remove Policy Map Data
      if( opts.locType == "Z" ){
        app.api.getPostalCodeGeometry( opts.locId, function ( err, geometry ) {
          app.leaflet.clearPostalCodeBoundary();
          if( err ){
            return;
          }
          if (geometry) {
            app.leaflet.showPostalCodeBoundary(geometry);
          }
        });
      }
      */

      const inMemberListings = window.location.href.indexOf('/member-listings') !== -1;
      if (inMemberListings) {
        const locationStr = ''.concat(Lat, ',', Lon, ',', (radius || app.leaflet.opts.circleDefaultRadius));
        const route = `member-listings/${locationStr}`;
        return app.router.go(route);
      }

      if (!Menu.sharedCursor.get(['agent', 'data'])) {
        app.actions.common.flagUserAsInteractedWithSite();
        return app.actions.home.onNav(Lat, Lon, radius, query, null, opts);
      }

      if (Lat && Number(Lat) && Lon && Number(Lon)) {
        app.actions.map.resetToInitialZoomOnRoute();

        const inGrid = window.location.href.indexOf('/grid') !== -1;

        const locationStr = ''.concat(Lat, ',', Lon, ',', (radius || app.leaflet.opts.circleDefaultRadius));

        let route = '/'.concat(Menu.sharedCursor.get(['agent', 'Id']),
          '/search/',
          inGrid ? 'grid/' : 'map/', locationStr,
          (opts && opts.listingId) ? `/${opts.listingId}` : '');

        if (opts.currentLocation) {
          route = route.replace(/,\d+$/, ',1000');
          Menu.sharedMenuCursor.set('sortType', 'distance_asc');
        }

        if (query) {
          app.actions.common.saveLocationQuery(query);
        }

        app.cursor.set(['panels', 'listings', 'meta', 'locationStr'], locationStr);
        app.cursor.set(['panels', 'listings', 'data'], null);

        app.actions.common.setLocationType(opts);
        app.cursor.commit();

        app.router.go(route);
      }
    },
  };

  Menu.geocodeAddress = function (address, cb) {
    if (Menu.onAutosuggest.nlpIdentified) {
      return;
    }
    if (!address || address.length < 4) {
      return;
    }
    app.api.geocodeAddress(address, (res) => {
      if (res && typeof res === 'object') {
        Menu.onAutosuggest.onNav(res.geometry.location.lat, res.geometry.location.lng, null, res.formatted_address);
        cb(res);

        // TODO
        // Geocodio does not allow quering by place name or return postal_code as type. Skipping the logic below for now.
        // One improvement is to use first item

        /*
        // if it's a zip
        if (res.types && _.includes(res.types, 'postal_code')) {
          const postalCodeComponent = _.find(res.address_components, (component) => _.includes(component.types, 'postal_code'));

          app.api.getPostalCodeGeometry(postalCodeComponent.long_name, (err, geometry) => {
            if (geometry) {
              app.leaflet.showPostalCodeBoundary(geometry);
            } else {
              app.leaflet.clearPostalCodeBoundary();
            }
          });
        }
        // if it's a school
        else if (res.types && _.includes(res.types, 'school')) {
          // We have to do this a little indrectly because the google result doesn't
          // return a NCES code, which is how we look up the geometry from spacial
          // SpatialStream. So we will find all the schools for the lat/lng of the
          // google result, and then assume it's the closest school to the lat/lng.

          const state = _.find(res.address_components, (component) => _.includes(component.types, 'administrative_area_level_1'));

          const stateName = state.long_name;

          // get all of the schools near the google result
          app.api.publicSchoolsByGeo(
            res.geometry.location.lat,
            res.geometry.location.lng,
            stateName,
            (schools) => {
              // find the closest school to the lat/lng and draw that one, because it's probably the correct one
              const closestSchool = _(schools)
                .sortBy((record) => record.school.distance)
                .first()
                .valueOf();

              const geometry = _.get(closestSchool, 'school.geometry');

              app.leaflet.addSchoolMarker(
                {
                  gsId: _.get(closestSchool, 'school.gsId'),
                  lat: _.get(closestSchool, 'school.lat'),
                  lon: _.get(closestSchool, 'school.lon'),
                  name: _.get(closestSchool, 'school.name'),
                  geometry: _.get(closestSchool, 'school.geometry'),
                },
              );

              if (geometry) {
                app.leaflet.showSchoolAttendanceBoundary(closestSchool.name, geometry);
              } else {
                app.leaflet.clearSchoolAttendanceBoundary();
              }
            },
          );
        }
        // if it's a neighborhood
        else if (res.types && _.includes(res.types, 'neighborhood')) {
          app.api.neighborhoodGeometryByGeo(res.geometry.location.lat, res.geometry.location.lng, (neighborhoodName, geometry) => {
            if (geometry) {
              app.leaflet.showNeighborhoodBoundary(neighborhoodName, geometry);
            } else {
              app.leaflet.clearNeighborhoodBoundary();
            }
          });
        }
        */
      }
    });
  };

  Menu.reverseGeocodeCoord = function (coord, cb) {
    app.api.reverseGeocodeCoord(coord, (res) => {
      if (res && typeof res === 'object') {
        cb(res);
      }
    });
  };

  Menu.setEditingFilters = function (showingFilters) {
    if (Menu.sharedCursor.get('showFilters') !== !!showingFilters) {
      Menu.sharedCursor.set('showFilters', !!showingFilters);
      app.cursor.commit();
    }
  };

  return Menu;
};
