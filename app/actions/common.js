import agentPickerContext from '../models/agentPickerContext';

import Location from '../models/location';

const scrollTo = require('scroll-to');
const querystring = require('querystring');
const _ = require('lodash');
const moment = require('moment');
const copy = require('clipboard-copy');

module.exports = function (app) {
  function Common() {
  }

  Common.VIEWED_PROPERTIES_LS = 'V';
  Common.SAVED_SEARCH_LS = 'SAVED_S';
  Common.PHOTO_SLIDER_OPTIONS_LS = 'P';
  Common.LOGIN_MODAL_CNT_LS = 'LOGIN_MODAL_CNT';

  Common.SESSION_VIEWED_PROPERTY_COUNT_LS = 'SV_A';

  Common.FLAG_CAME_FROM_FACEBOOK_AD_KEY = 'FLAG_CAME_FROM_FACEBOOK_AD';
  Common.FLAG_FORCE_LOGIN_FIRST_INTERACTION = 'FLAG_FORCE_LOGIN_FIRST_INTERACTION';
  Common.FLAG_NO_ONBOARDING_KEY = 'FLAG_NO_ONBOARDING';
  Common.FLAG_USER_HAS_INTERACTED_WITH_APP_KEY = 'FLAG_USER_HAS_INTERACTED_WITH_APP';
  Common.FLAG_FORCED_FIRST_CHARGE_KEY = 'FLAG_FORCED_FIRST_CHARGE';
  Common.FLAG_FORCED_DREAM_SWEEPS = 'FLAG_FORCED_DREAM_SWEEPS';
  Common.FLAG_DELIVERED_FREE_LEAD = 'FLAG_DELIVERED_FREE_LEAD';
  Common.FLAG_NO_CHARGE_FOR_LEADS_KEY = 'FLAG_NO_CHARGE_FOR_LEADS';
  Common.FLAG_USE_EMPLOYEE_UI_KEY = 'FLAG_USE_EMPLOYEE_UI';

  Common.SESSION_COUNT_KEY = 'SESSION_COUNT';

  Common.sharedCursor = app.cursor.select('shared');
  Common.layoutCursor = app.cursor.select('layout');
  Common.loadingCursor = app.cursor.select('loading');

  Common.onInit = function () {
    Common.getViewedProperties();
    Common.getPhotoSliderOptions();
    app.actions.tagging.moveLocalTagsToTree();
    app.actions.tagging.getAllTags(true);

    window.onbeforeunload = function () {
      Common.logLastUserNavigationTimestamp();
    };

    Common.parseQueryParameters();
    Common.checkToLoadEmployeeUi();

    Common.incrementSessionCount();

    app.actions.read.fetchREADAgent();

    Common.fetchRateplugInformation();

    if (window.GUID) {
      app.actions.login.authApi(window.GUID);
    } else {
      window.HomeASAPAccountsAddInitCallback(
        () => {
          app.actions.login.checkLoginStatus();
        },
      );
    }

    Common.registerBrowserNotifications();

    Common.registerShortcuts();
  };

  Common.fetchRateplugInformation = function () {
    //     for (const q of ['rp_rate', 'rp_apr', 'rp_program', 'rp_payment', 'rp_downpayment', 'rp_buyer', 'rp_lo', 'rp_onboarding', 'rp_veteran', 'rp_va_loan_limit']) {

    if (window.rateplug.rp_buyer) {
      app.api.getRateplugCustomerInfo(window.rateplug.rp_buyer, (err, res) => {
        if (err) {
          return console.error('getRateplugCustomerInfo failed', err);
        }
        if (res) {
          const loanOfficer = res.LoanOfficer || {};
          if (loanOfficer.LOGUID) {
            window.rateplug.rp_lo = loanOfficer.LOGUID;
          }
          if (loanOfficer.Rate) {
            window.rateplug.rp_rate = loanOfficer.Rate;
          }
          if (loanOfficer.APR) {
            window.rateplug.rp_apr = loanOfficer.APR;
          }
          if (loanOfficer.VALoanLimit) {
            window.rateplug.rp_va_loan_limit = loanOfficer.VALoanLimit;
          }
        }
      });
    }
  };

  Common.registerShortcuts = function () {
    document.addEventListener('keydown', (e) => {
      if ((event.ctrlKey || event.metaKey) && event.which == 80) {
        // Ctrl-P or Command-P
        e.preventDefault();

        const listing = Common.getActiveListing();
        if (listing && listing.Id) {
          Common.printListingById(listing.Id);
        }

        return false;
      }
    });
  };

  Common.incrementSessionCount = function () {
    const sessionCount = Common.getSessionCount();
    window.localStorageAlias.setItem(Common.SESSION_COUNT_KEY, sessionCount + 1);
  };

  Common.getSessionCount = function () {
    const sessionCount = Number(window.localStorageAlias.getItem(Common.SESSION_COUNT_KEY));
    if (Number.isNaN(sessionCount)) {
      return 0;
    }
    return sessionCount;
  };

  Common.bindWatchers = function () {
    const agentPickerCursor = app.cursor.select(['shared', 'agentPickerContext']);
    const onAgentPickercontextUpdate = (e) => {
      if (e && e.data && e.data.data) {
        const context = e.data.data;
        const previousContext = e.data.previousData;
        if (!agentPickerContext.isSignificantChange(context, previousContext)) {
          console.log('NOT Calling Agent Picker - context is unchanged');
        } else {
          console.log('Calling Agent Picker due to context change');
          context.currentAgentId = Common.getAgentId();
          context.onboardingAgentId = Common.getOnboardingAgentId();
          app.api.pickAgent(context, (err, agent, res) => {
            if (err) {
              return console.error('pick agent failed', err);
            }
            let trackingId = '';
            if (res && res.headers && res.headers['x-trackingid']) {
              trackingId = res.headers['x-trackingid'];
            }

            app.models.agentPicker.switchAgent(agent, trackingId);
          });
        }
      }
    };
    agentPickerCursor.on('update', _.debounce(onAgentPickercontextUpdate, 1000));

    const locationCursor = app.cursor.select(['shared', 'location']);
    locationCursor.on('update', (e) => {
      if (e && e.data) {
        const currentLocation = e.data.data;
        if (!currentLocation) {
          agentPickerContext.setLocation(null);
        } else if (!_.isEqual(currentLocation, e.data.previousData)) {
          agentPickerContext.setLocation(currentLocation.lat, currentLocation.lng);
        }
      }
    });

    const listingsPanelCursor = app.cursor.select(['panels', 'listings', 'data']);
    listingsPanelCursor.on('update', (e) => {
      if (e && e.data) {
        const listings = e.data.data;
        const activeListing = Common.getActiveListing();
        if (!activeListing) {
          agentPickerContext.setMlsIdsFromListings(listings);
        }
      }
    });
  };

  Common.setLocation = function (route) {
    const currentLocation = Location.fromString(route.params.location);

    Common.sharedCursor.set(['location'], currentLocation);
    app.cursor.commit();
  };

  Common.getMlsIds = function (listings) {
    if (!listings) {
      return [];
    }

    if (!Array.isArray(listings)) {
      listings = [listings];
    }

    let ids = listings.reduce((mlsIds, listing) => {
      if (listing && listing.MlsIds) {
        mlsIds = _.union(mlsIds, listing.MlsIds);
      }
      return mlsIds;
    }, []);

    ids = _.filter(ids);
    return ids;
  };

  Common.flagUserAsInteractedWithSite = function () {
    if (!Common.interacted()) {
      app.actions.analytics.sendEvent('login', 'engagement', Common.getAgentId(), '');
    }

    agentPickerContext.setInteracted(true);
  };

  Common.interacted = function () {
    return window.sessionStorageAlias.getItem(Common.FLAG_USER_HAS_INTERACTED_WITH_APP_KEY);
  };

  Common.userCameFromFacebookAd = function () {
    return window.sessionStorageAlias.getItem(Common.FLAG_CAME_FROM_FACEBOOK_AD_KEY);
  };

  Common.forceLoginFirstInteraction = function () {
    return window.sessionStorageAlias.getItem(Common.FLAG_FORCE_LOGIN_FIRST_INTERACTION);
  };

  Common.useEmployeeUi = function () {
    return window.localStorageAlias.getItem(Common.FLAG_USE_EMPLOYEE_UI_KEY);
  };

  Common.enableEmployeeUi = function () {
    window.localStorageAlias.setItem(Common.FLAG_USE_EMPLOYEE_UI_KEY, true);
    Common.checkToLoadEmployeeUi();
  };

  Common.disableEmployeeUi = function () {
    window.localStorageAlias.removeItem(Common.FLAG_USE_EMPLOYEE_UI_KEY);
    Common.checkToLoadEmployeeUi();
  };

  Common.forceDreamSweeps = function () {
    return !!window.sessionStorageAlias.getItem(Common.FLAG_FORCED_DREAM_SWEEPS);
  };

  Common.noOnboarding = function () {
    return window.sessionStorageAlias.getItem(Common.FLAG_NO_ONBOARDING_KEY);
  };

  Common.haveForcedFirstCharge = function () {
    return window.sessionStorageAlias.getItem(Common.FLAG_FORCED_FIRST_CHARGE_KEY);
  };

  Common.setForcedFirstCharge = function (value = 1) {
    return window.sessionStorageAlias.setItem(Common.FLAG_FORCED_FIRST_CHARGE_KEY, value);
  };

  Common.haveDeliveredFreeLead = function () {
    return window.sessionStorageAlias.getItem(Common.FLAG_DELIVERED_FREE_LEAD);
  };

  Common.setDeliveredFreeLead = function (value = 1) {
    return window.sessionStorageAlias.setItem(Common.FLAG_DELIVERED_FREE_LEAD, value);
  };

  Common.checkToLoadEmployeeUi = function () {
    if (Common.useEmployeeUi()) {
      app.cursor.set(['layout', 'employeeUi'], true);
      app.cursor.commit();
    } else {
      app.cursor.set(['layout', 'employeeUi'], false);
      app.cursor.commit();
    }
  };

  /** *
   * Router
   */

  // Common.routerPreStart = function() {
  //  console.log("Common.routerPreStart")
  // }

  // Common.routerPostStart = function() {
  //  console.log("Common.routerPostStart")
  // }

  Common.setPageTitle = function (title) {
    document.title = title || 'Home is closer than you think.';
  };

  Common.modifyInstalledAppName = function (appName = 'HomeASAP') {
    const iosMetaTag = document.querySelector('meta[name=apple-mobile-web-app-title]');
    iosMetaTag.setAttribute('content', appName);
    const androidMetaTag = document.querySelector('meta[name=application-name]');
    androidMetaTag.setAttribute('content', appName);
  };

  Common.resetPageTitle = function () {
    Common.setPageTitle();
  };

  Common.parseQueryParameters = function () {
    const params = Common.getQueryParameters();

    if (params.facebook_ad) {
      window.sessionStorageAlias.setItem(Common.FLAG_CAME_FROM_FACEBOOK_AD_KEY, true);
      agentPickerContext.addSource('advertisement');
    }

    if (params.force_login) {
      window.sessionStorageAlias.setItem(Common.FLAG_FORCE_LOGIN_FIRST_INTERACTION, 'force_login');
    }

    if (params.ppc) {
      // pay per click ad force login on first interaction
      window.sessionStorageAlias.setItem(Common.FLAG_FORCE_LOGIN_FIRST_INTERACTION, 'ppc');
    }

    if (params.fds) {
      window.sessionStorageAlias.setItem(Common.FLAG_FORCED_DREAM_SWEEPS, true);
    }

    if (params.nob) {
      window.sessionStorageAlias.setItem(Common.FLAG_NO_ONBOARDING_KEY, true);
    }

    if (params.ncfl) {
      window.sessionStorageAlias.setItem(Common.FLAG_NO_CHARGE_FOR_LEADS_KEY, true);
      agentPickerContext.setForceFree(true);
    }

    /* don't ever use FLAG_USE_EMPLOYEE_UI for anything that should require actual permission because it is client based.
    For example, this is currently being implemented to highlight an agent's featured listings for TKS.
    */

    if (params.employee) {
      Common.enableEmployeeUi();
      Common.checkToLoadEmployeeUi();
    }
  };

  Common.targetOverwrite = (() => {
    if (window.rateplug.rp_buyer) {
      return 'self';
    }
    const matches = window.location.search.match(/target=(self|blank)/);
    return matches && matches[1] || null;
  })();

  Common.forceMobile = (() => !!window.location.search.match(/forcemobile=1/))();

  Common.goToRoute = function (route, params) {
    const agentId = Common.sharedCursor.get(['agent', 'data', 'CustomURL']) || Common.sharedCursor.get(['agent', 'Id']);

    // agentId = agentId || '~'
    if (!agentId) {
      console.warn('Common.goToRoute does not have an agent id.');
      return;
    }

    const _route = `/${agentId}${route}`;
    if (Common.targetOverwrite === 'self' || !app.utils.inIFrame()) {
      app.router.go(_route, params);
    } else if (!(params && params.skip)) {
      const newRoute = (window.BASE_URL === '/' ? '' : window.BASE_URL) + _route;
      window.open(newRoute, '_blank');
    }

    if (params && params.skip) {
      app.actions.analytics.sendPageView();
    }
  };

  Common.facebookRoute = function (route) {
    const agentId = Common.sharedCursor.get(['agent', 'Id']);
    // agentId = agentId || '~'
    if (!agentId) {
      console.warn('Common.facebookRoute does not have an agent id.');
      return;
    }

    const _route = `/${agentId}${route}`;
    app.router.go(_route);
  };

  Common.goToAboutHomeasap = function () {
    window.open('https://about.homeasap.com', '_blank');
  };

  Common.goToListing = function (listing) {
    if (!(listing && listing.Location && listing.Location.Lat && listing.Location.Lon)) {
      console.warn('Attempting to navigate to a listing with invalid location');
      return;
    }
    Common.goToRoute(`/search/map/${listing.Location.Lat},${listing.Location.Lon},4000/${listing.Id}`);
  };

  Common.goToAgentListing = function (listing) {
    if (!(listing && listing.Location && listing.Location.Lat && listing.Location.Lon)) {
      console.warn('Attempting to navigate to a listing with invalid location');
      return;
    }
    Common.goToRoute(`/agent/${listing.Id}`);
  };

  Common.startedOnHomePage = function () {
    if (app.router.history && app.router.history[0] && (app.router.history[0].url == '/')) {
      return true;
    }
    return false;
  };

  Common.goToHomePage = function (params) {
    if (Common.startedOnHomePage()) {
      // go to home page
      app.router.go('/', params);
    } else if (Common.getOnboardingAgentId()) {
      // go to onboarding agent landing page
      app.router.go(`/${Common.getOnboardingAgentId()}`);
    } else {
      // go to current agent landing page
      Common.goToRoute('/');
    }
  };

  Common.saveAgentId = function (newAgentId, newAgentData, cb) {
    if (!newAgentId) {
      if (cb) {
        return cb(new Error('No Agent Id Found'), false);
      }
    }

    Common.sharedCursor.set(['agent', 'Id'], newAgentId);

    const setAgent = (res) => {
      if (Common.sharedCursor.get(['agent', 'data', 'Id']) !== (res && res.Id)) {
        Common.sharedCursor.set(['agent', 'data'], res);
        app.cursor.commit();
        document.body.dataset.agentmlsid = res.MlsId;

        // Update the sale type based on agent settings on hide sale/hide rent
        if (res && res.AgentSettings) {
          const hideSale = app.utils.getAgentSettingValue(res.AgentSettings, 'alphidesale') === 'true';
          const hideRent = app.utils.getAgentSettingValue(res.AgentSettings, 'alphiderent') === 'true';

          if (hideSale && !hideRent) {
            // Force For Rent if For Sale is hidden
            Common.sharedCursor.set(['menu', 'saleType'], 2);
          } else if (!hideSale && hideRent) {
            // Force For Sale if For Rent is hidden
            Common.sharedCursor.set(['menu', 'saleType'], 1);
          }
          app.cursor.commit();
        }
      }

      if (res.MlsId) {
        const existingMlsData = Common.sharedCursor.get(['mlsData', res.MlsId]);
        if (existingMlsData) {
          Common.sharedCursor.set(['agent', 'mlsData'], existingMlsData);
        } else {
          app.actions.listing.getMlsData(res.MlsId, (mlsData) => {
            if (mlsData) {
              Common.sharedCursor.set(['agent', 'mlsData'], mlsData);
            }
          });
        }
      }
    };

    const agentData = Common.sharedCursor.get(['agent', 'data']);
    if (!agentData
      || (
        agentData.Id != newAgentId
        && (agentData.CustomURL || '').replace(/[^a-zA-Z0-9]/, '') != String(newAgentId).replace(/[^a-zA-Z0-9]/, '')
      )
    ) {
      // Common.sharedCursor.set(['agent', 'data'], null);

      // Reset Agent Stuff
      /*
      app.utils.updateCursor({
        cursor: app.cursor.select(['screens']),
        defaults: app.cursorDefaults.screens,
        finalState: {}
      });
      */
      if (newAgentData) {
        setAgent(newAgentData);
        if (cb) {
          cb(null, newAgentData);
        }
      } else {
        const headerCursor = app.cursor.select(['panels', 'header']);
        headerCursor.set('loadingAgent', true);
        app.cursor.commit();

        app.api.agent(newAgentId, (res) => {
          headerCursor.set('loadingAgent', false);
          app.cursor.commit();

          if (res && typeof res === 'object') {
            const detectedAgentData = app.actions.read.getDetectedAgentData();
            const detectedAgentId = detectedAgentData && detectedAgentData.agentId;
            if (detectedAgentId === res.Id) {
              res.IsSearchAllianceMemberOld = res.IsSearchAllianceMember;
              res.IsSearchAllianceMember = false;
            }

            setAgent(res);

            if (cb) {
              cb(null, res);
            }
          } else {
            // Agent Data failed to be retrieved
            if (cb) {
              cb(new Error('Agent Id Request Error'), false);
            }
          }
        });
      }
    } else {
      setAgent(agentData);

      if (cb) {
        cb(null, agentData);
      }
    }

    app.cursor.commit();
    console.log(`Saved agent id ${Common.sharedCursor.get(['agent', 'Id'])}`);
  };

  Common.fetchAgentCount = function () {
    const agentCount = Common.sharedCursor.get('agentCount');
    if (!agentCount) {
      app.api.agentCount((statusCode, res) => {
        if (res) {
          Common.sharedCursor.set('agentCount', res);
          app.cursor.commit();
        }
      });
    }
  };

  Common.getAgentId = function () {
    return Common.sharedCursor.get(['agent', 'data', 'Id']);
  };

  Common.servicesOverwrite = {}; // { [agentId]: [] }

  Common.getAgentHasIDX = function () {
    return Common.sharedCursor.get(['agent', 'data', 'HomeSearchRegisteredDateTime']);
  };

  Common.getAgentHasHV = function () {
    return new Set([
      ...Common.servicesOverwrite[Common.getAgentId()] || [],
      ...Common.sharedCursor.get(['agent', 'data', 'ProductServices']) || []] || []).has('HV');
  };

  Common.getAgentHasDS = function () {
    return new Set([
      ...Common.servicesOverwrite[Common.getAgentId()] || [],
      ...Common.sharedCursor.get(['agent', 'data', 'ProductServices']) || []] || []).has('DS');
  };

  Common.getAgentHasLendingTree = function (/* agentData */) {
    // NPLAY-7247 Disabling LendingTree Modal as it's being discontinued

    return false;

    // if (['rateplug', 'more'].includes(document.body.dataset.theme)) {
    //   return false;
    // }

    // if (agentData) {
    //   if ((agentData.MlsId || '').toLowerCase().startsWith('on')) {
    //     return false;
    //   }
    //   if (agentData.MemberType === 2) {
    //     return false;
    //   }
    //   if (agentData.AgentSettings) {
    //     return app.utils.getAgentSettingValue(agentData.AgentSettings, 'disable_lending_tree') !== 'true';
    //   }
    // }
    // return false;
  };

  Common.getAgentData = function () {
    return Common.sharedCursor.get(['agent', 'data']);
  };

  Common.getLoginState = function () {
    return Common.sharedCursor.get(['login', 'state']);
  };

  Common.setLoginState = function (state) {
    Common.sharedCursor.set(['login', 'state'], state);
    app.cursor.commit();
  };

  Common.isLoggedIn = function () {
    return Common.getBuyerId();
  };

  Common.getBuyerId = function () {
    return Common.sharedCursor.get(['buyer', 'Id']);
  };
  Common.setBuyerId = function (id) {
    Common.sharedCursor.set(['buyer', 'Id'], id);
    app.cursor.commit();

    if (Common.getInBrowserNotificationBeta() && window.OneSignal) {
      OneSignal.push(() => {
        OneSignal.sendTag('buyerId', id || '');
      });
    }
  };

  Common.getBuyerData = function () {
    return Common.sharedCursor.get(['buyer', 'data']);
  };
  Common.setBuyerData = function (data) {
    Common.sharedCursor.set(['buyer', 'data'], data);
    app.cursor.commit();
  };

  Common.resetBuyerData = function () {
    app.actions.common.setBuyerId(null);
    app.actions.common.setBuyerData({ name: null, id: null });
  };

  Common.getViewedProperties = function () {
    const viewedProperties = JSON.parse(window.localStorageAlias.getItem(Common.VIEWED_PROPERTIES_LS)) || {};
    Common.sharedCursor.set('viewedProperties', viewedProperties);
    return viewedProperties;
  };

  Common.setPropertyViewed = function (listing) {
    Common.setPageTitle(''.concat(
      listing.Address.FullStreetAddress, ', ',
      listing.Address.CityName, ', ',
      listing.Address.State, ' ', listing.Address.ZipCode,
    ));

    if (!Common.sharedCursor.get(['viewedProperties', listing.Id])) {
      Common.sharedCursor.set(['viewedProperties', listing.Id], 1);
      app.cursor.commit();

      window.localStorageAlias.setItem(Common.VIEWED_PROPERTIES_LS, JSON.stringify(Common.sharedCursor.get('viewedProperties')));

      setTimeout(() => {
        const agentData = Common.getAgentData();
        app.actions.leads.addIDXLead(listing.PropertyListingId, agentData && agentData.MlsId);

        app.actions.common.setNotificationHeader('Viewed Homes',
          _.filter(Common.sharedCursor.get('viewedProperties'), (viewed) => viewed).length);
      }, 100);
    }
  };

  Common.getLocalSavedSearches = function () {
    return JSON.parse(window.localStorageAlias.getItem(Common.SAVED_SEARCH_LS)) || {};
  };

  Common.addLocalSavedSearch = function (savedSearch) {
    const savedSearches = Common.getLocalSavedSearches();
    savedSearches[savedSearch.SearchText] = savedSearch;

    Common.setNotificationHeader('Recent Searches', Object.keys(savedSearches).length);

    window.localStorageAlias.setItem(Common.SAVED_SEARCH_LS, JSON.stringify(savedSearches));
  };

  Common.saveLocationQuery = function (locationQuery, shouldCommit) {
    console.log(`Setting location query route to: ${locationQuery}`);
    app.cursor.set(['panels', 'listings', 'meta', 'locationQuery'], locationQuery);

    if (shouldCommit) {
      app.cursor.commit();
    }
  };

  Common.getLocationQuery = function () {
    return app.cursor.get(['panels', 'listings', 'meta', 'locationQuery']);
  };

  Common.isLocationQueryPlaceholder = function (query) {
    return (query || Common.getLocationQuery()) === 'Current location based on map coordinates';
  };

  Common.getPhotoSliderOptions = function () {
    app.cursor.set(['user', 'photoSliderOptions'],
      JSON.parse(window.localStorageAlias.getItem(Common.PHOTO_SLIDER_OPTIONS_LS))
      || { play: false, thumbnails: true });
  };

  Common.setPhotoSliderOptions = function (options) {
    app.cursor.set(['user', 'photoSliderOptions'], options);
    window.localStorageAlias.setItem(Common.PHOTO_SLIDER_OPTIONS_LS, JSON.stringify(options));
    app.cursor.commit();
  };

  Common.getPhotoCaptions = function (listing) {
    if (listing.REALData) {
      listing.Captions = _.map(listing.Images, (Image) => Image.Caption);

      app.cursor.set(['shared', 'photo', 'captions'], listing);
      app.cursor.commit();
    } else {
      app.api.listingImageCaptions(listing, (res) => {
        if (res && typeof res === 'object') {
          app.cursor.set(['shared', 'photo', 'captions'], res);
          app.cursor.commit();
        }
      });
    }
  };

  Common.toggleModal = function (modalName) {
    const newVal = app.cursor.get(['layout', 'modal']) === modalName ? false : modalName;
    app.cursor.set(['layout', 'modal'], newVal);
    app.cursor.commit();
  };

  Common.showHelpOverlay = function () {
    app.cursor.set(['layout', 'help', 'overlay'], true);
    app.cursor.commit();
  };

  Common.hideHelpOverlay = function () {
    app.cursor.set(['layout', 'help', 'overlay'], false);
    app.cursor.commit();
  };

  /** *
   * UI Related Utils
   */

  /*
   * Photo Slider Index Tracking
   */

  Common.setPhotoSliderIndex = function (index) {
    if (Common.sharedCursor.set('photoSliderIndex') !== index) {
      console.log(`Setting Photo Slider to ${index}` || 0);
      Common.sharedCursor.set('photoSliderIndex', index || 0);
      app.cursor.commit();
    }
  };

  /*
   * Animate Scroll
   */

  Common.scrollElementTo = function (element, to, duration) {
    if (duration < 0) {
      return;
    }
    if (typeof to === 'object') {
      to = to.offsetTop;
    }

    scrollTo(0, to, {
      ease: 'inSine',
      duration: duration || 500,
      target: element,
    });
  };

  Common.showShareForm = function (data) {
    Common.sharedCursor.set(['shareForm', 'show'], true);
    Common.sharedCursor.set(['shareForm', 'listingData'], data);
    app.cursor.commit();
    console.log(Common.sharedCursor.get(['shareForm', 'show']));

    app.actions.analytics.sendEvent('detail view', 'share', data && data.ZipCode);
  };

  Common.hideShareForm = function (clearData) {
    console.log(`cleardata${clearData}`);
    Common.sharedCursor.set(['shareForm', 'show'], false);
    if (clearData) {
      Common.sharedCursor.set(['shareForm', 'listingData'], null);
    }
    app.cursor.commit();
  };

  Common.submitShare = function (shareName, shareEmail, shareMessage, buyerName, buyerEmail, id, agentId, cb) {
    app.api.shareProperty(shareName, shareEmail, shareMessage, buyerName, buyerEmail, id, agentId, cb);
  };

  Common.showBlankModal = function () {
    Common.layoutCursor.set(['blankModal'], true);
    app.cursor.commit();
  };

  Common.hideBlankModal = function () {
    Common.layoutCursor.set(['blankModal'], false);
    app.cursor.commit();
  };

  Common.showResultsHelp = function () {
    console.log('hitting show rsult help');
    Common.layoutCursor.set(['showResultsHelp'], true);
    app.cursor.commit();
  };

  Common.hideResultsHelp = function () {
    Common.layoutCursor.set(['showResultsHelp'], false);
    app.cursor.commit();
  };

  Common.toggleSidebar = function () {
    Common.setSidebar(!Common.layoutCursor.get(['sidebar']));
  };

  Common.setSidebar = function (display) {
    Common.layoutCursor.set(['sidebar'], display);
    app.cursor.commit();
  };

  Common.enableBodyScroll = function () {
    document.body.classList.remove('no-scroll');
    document.documentElement.style.removeProperty('overflow');
    if (app.utils.isIos()) {
      document.documentElement.style.removeProperty('position');
    }
    // document.removeEventListener("touchmove", preventBodyScroll );
  };

  Common.disableBodyScroll = function () {
    document.body.classList.add('no-scroll');
    document.documentElement.style.setProperty('overflow', 'hidden');
    if (app.utils.isIos()) {
      document.documentElement.style.setProperty('position', 'fixed');
    }
    // document.addEventListener("touchmove", preventBodyScroll );
  };

  Common.setOnboardingAgent = function (agent) {
    // dont set onboarding agent if we came from a facebook ad, or nob query parameter was set
    if (Common.userCameFromFacebookAd() || Common.noOnboarding()) {
      return;
    }

    try {
      window.localStorageAlias.setItem('ONBOARDING_AGENT', JSON.stringify(agent));
      if (agent) {
        app.actions.analytics.sendEvent('agent switch', 'onboarded agent set', agent.CustomURL || agent.Id, app.actions.common.getBuyerId());
      }
    } catch (e) {
      console.warn('attempting to set onboarding agent with an invalid value of ', agent);
    }
  };

  Common.getOnboardingAgent = function () {
    try {
      return JSON.parse(window.localStorageAlias.getItem('ONBOARDING_AGENT'));
    } catch (e) {
      return false;
    }
  };

  Common.getOnboardingAgentHasIDXPlus = function () {
    // Ignore IDX Plus Logic when in rateplug mode
    if (window.rateplug.rp_buyer) {
      return false;
    }

    const onboardingAgent = Common.getOnboardingAgent();

    if (onboardingAgent) {
      if ((onboardingAgent.ProductServices || []).includes('IDX+')) {
        return true;
      }

      if (onboardingAgent.AgentSettings) {
        return app.utils.getAgentSettingValue(onboardingAgent.AgentSettings, 'agent_setting_stripe__has_idx_plus') === 'true';
      }
    }

    return false;
  };

  Common.setPendingOnboardingAgentId = function (id) {
    Common.pendingOnboardingAgentId = id;
  };

  Common.setPendingOnboardingAgent = function (agent) {
    if (Common.pendingOnboardingAgentId && agent && agent.Id == Common.pendingOnboardingAgentId) {
      Common.setOnboardingAgent(agent);
      Common.pendingOnboardingAgentId = null;
    }
  };

  Common.getOnboardingAgentId = function () {
    if (window.rateplug.rp_buyer) {
      return Common.getAgentId();
    }
    const onboardingAgent = Common.getOnboardingAgent();
    return (onboardingAgent && onboardingAgent.Id);
  };

  Common.clearOnboardingAgent = function () {
    window.localStorageAlias.removeItem('ONBOARDING_AGENT');
  };

  Common.getOnboardingAgentMlsId = function () {
    const onboardingAgent = Common.getOnboardingAgent();
    return (onboardingAgent && onboardingAgent.MlsId);
  };

  Common.setLocationCustomName = function (lat, lon, name) {
    window.localStorageAlias.setItem(`LocationCustomName:${
      String(lat || '').replace(/^0+|\.?0+$/g, '')
    },${
      String(lon || '').replace(/^0+|\.?0+$/g, '')}`,
    name);
  };

  Common.getLocationCustomName = function (lat, lon) {
    return window.localStorageAlias.getItem(`LocationCustomName:${
      String(lat || '').replace(/^0+|\.?0+$/g, '')
    },${
      String(lon || '').replace(/^0+|\.?0+$/g, '')}`) || null;
  };

  Common.blurContent = function () {
    app.cursor.set(['shared', 'blurContent'], true);
    app.cursor.commit();
  };

  Common.unblurContent = function () {
    app.cursor.set(['shared', 'blurContent'], false);
    app.cursor.commit();
  };

  Common.backToHome = function () {
    app.router.go('/'.concat(Common.getOnboardingAgentId() || ''));
  };

  Common.getInBrowserNotificationBeta = function () {
    if (Common.getQueryParameters().pushbeta) {
      window.localStorageAlias.setItem('PUSH_NOTIFICATION_BETA', 1);
      return true;
    }
    return !!window.localStorageAlias.getItem('PUSH_NOTIFICATION_BETA');
  };

  Common.registerBrowserNotifications = function () {
    if (Common.getInBrowserNotificationBeta()) {
      window.OneSignal = window.OneSignal || [];
      OneSignal.push(() => {
        OneSignal.init({
          appId: window.CONFIG.IN_PROD ? 'bff08d79-266f-4684-a347-5923cd27edc9' : 'b2de92df-c9f1-427c-aa6b-b23b351b9f26',
        });
        OneSignal.sendTag('env', window.CONFIG.IN_PROD ? 'prod' : 'test');
      });
    }
  };

  Common.setExtraPermissions = function (permissions) {
    Common.sharedCursor.set(['login', 'extraPermissions'], permissions);
    app.cursor.commit();
  };

  Common.setFriendsPermissions = function () {
    Common.sharedCursor.set(['buyer', 'friendsPermission'], true);
    app.cursor.commit();
  };

  Common.incrementSessionViewedPropertyCount = function (listingId) {
    const properties = Common.getSessionViewedProperties() || {};
    properties[listingId] = true;
    window.sessionStorageAlias.setItem(Common.SESSION_VIEWED_PROPERTY_COUNT_LS, JSON.stringify(properties));
  };

  Common.getSessionViewedProperties = function () {
    try {
      return JSON.parse(window.sessionStorageAlias.getItem(Common.SESSION_VIEWED_PROPERTY_COUNT_LS));
    } catch (e) {
      return false;
    }
  };

  Common.getSessionViewedPropertyCount = function () {
    const properties = Common.getSessionViewedProperties();
    return properties && Object.keys(properties) && Object.keys(properties).length || 0;
  };

  Common.metSessionViewedPropertyCountBeforeLogin = function () {
    // Show login automatically after # of properties are viewed AND user (buyer) is not logged in
    if (!Common.hasShownLoginModalAutomatically
      && !Common.isLoggedIn() && Common.getSessionViewedPropertyCount() === 1) {
      Common.hasShownLoginModalAutomatically = true;
      return true;
    }
    return false;
  };

  Common.setLocationType = function (opts) {
    if (opts && opts.locType && opts.locId) {
      app.cursor.set(['panels', 'map', 'locType'], opts.locType);
      app.cursor.set(['panels', 'map', 'locId'], opts.locId);
    }
  };

  Common.clearLocationType = function () {
    app.cursor.set(['panels', 'map', 'locType'], null);
    app.cursor.set(['panels', 'map', 'locId'], null);
  };

  Common.getLocationType = function () {
    return (
      {
        locType: app.cursor.get(['panels', 'map', 'locType']),
        locId: app.cursor.get(['panels', 'map', 'locId']),
      });
  };

  Common.getActiveListing = function () {
    let listing = null;
    if (!app.router.currentRoute) {
      return null;
    }
    switch (app.router.currentRoute.name) {
      case 'Map':
        listing = app.cursor.get(['screens', 'map', 'data']);
        break;
      case 'Grid':
        listing = _(app.cursor.get('screens', 'grid', 'data')).values().filter().first();
        break;
      case 'Listing':
        listing = app.cursor.get(['panels', 'listings', 'data']);
        break;
      case 'Agent':
        listing = app.cursor.get(['screens', 'agent', 'activeListing']);
        break;
      case 'Featured':
        listing = app.cursor.get(['screens', 'featured', 'detail']);
        break;
      default:
        break;
    }
    if (listing && (app.router.currentRoute.params.id == listing.Id || app.router.currentRoute.params.listingId == listing.Id)) {
      return listing;
    }

    return null;
  };

  Common.getVisibleListings = function () {
    let listings = [];
    let route = app.router.activeRoute;
    if (app.router.currentRoute) {
      route = app.router.currentRoute;
    }
    if (!route) {
      return [];
    }
    switch (route.name) {
      case 'Map':
      case 'Grid':
      case 'Listing':
        listings = app.cursor.get(['panels', 'listings', 'data']);
        break;
      case 'Agent':
      case 'Featured':
        listings = app.cursor.get(['screens', 'featured', 'data']);
        break;
      case 'Tagging':
        listings = _.map(app.cursor.get(['screens', 'tagging', 'data', 'Listings']), (listing) => listing.Listing);
        break;
      default:
        listings = [];
        break;
    }
    if (!listings) {
      listings = [];
    }
    return listings;
  };

  Common.getAllVisibleMlsIds = function () {
    const listings = Common.getVisibleListings();
    const ids = Common.getMlsIds(listings);
    return ids;
  };

  // get an array of all MLSs that are currently represented by the UI, based on listings and current agent.
  Common.getAllVisibleMlsArray = function () {
    const ids = Common.getAllVisibleMlsIds();
    let mlsArray = _.map(ids, (id) => app.cursor.get(['shared', 'mlsData', id]));
    mlsArray = _.filter(mlsArray);

    const agentMlsData = app.cursor.get(['shared', 'agent', 'mlsData']);
    if (agentMlsData) {
      mlsArray.push(agentMlsData);
    }

    return _.uniqBy(mlsArray, (mls) => mls.Id);
  };

  Common.getCurrentAgent = function () {
    return app.cursor.get(['shared', 'agent', 'data']);
  };

  Common.currentAgentIsOnboardingAgent = function () {
    const currentAgent = Common.getCurrentAgent();
    if (!currentAgent) {
      return false;
    }
    const currentAgentId = currentAgent.Id;

    const onboardingAgentId = Common.getOnboardingAgentId();
    if (!onboardingAgentId) {
      return false;
    }

    if (onboardingAgentId === currentAgentId) {
      return true;
    }

    return false;
  };

  Common.logLastUserNavigationTimestamp = function () {
    const now = moment().valueOf();
    window.localStorageAlias.setItem('LAST_USER_NAVIGATION_TIMESTAMP', now);
  };

  Common.getLastUserNavigationTimestamp = function () {
    return Number(window.localStorageAlias.getItem('LAST_USER_NAVIGATION_TIMESTAMP'));
  };

  Common.userHasNavigatedAfter = function (timestamp) {
    // guard clauses to make sure both exist
    if (!timestamp) {
      return false;
    }
    const lastUserNavigationTimestamp = Common.getLastUserNavigationTimestamp();
    if (!lastUserNavigationTimestamp) {
      return false;
    }

    // convert to a moment if it is a raw timestamp, otherwise leave it alone
    let checkTime = timestamp;
    if (!moment.isMoment(checkTime)) {
      checkTime = moment(checkTime);
    }

    // convert last nav to a moment
    const lastNav = moment(lastUserNavigationTimestamp);
    return lastNav.isAfter(checkTime);
  };

  Common.setNotificationHeader = function (label, count) {
    let totalCount = 0;
    _.forEach(Common.sharedCursor.get(['notificationCenter']), (val, key) => {
      if (key == 'header') {
        return;
      }
      let toAdd = 0;
      if (key == label) {
        toAdd = count;
      } else {
        toAdd = val;
      }
      if (typeof toAdd === 'number') {
        totalCount += toAdd;
      }
    });
    if (Common.isLoggedIn()) {
      if (Common.sharedCursor.get(['notificationCenter', 'header'])) {
        Common.sharedCursor.set(['notificationCenter', 'header'], null);
      }
    } else if (count > 0) {
      Common.sharedCursor.set(['notificationCenter', 'header'], { label, count, totalCount });
    }
    Common.sharedCursor.set(['notificationCenter', label], count);

    app.cursor.commit();
  };

  Common.getYelpCategories = function () {
    app.api.fetchYelpCategories((err, results) => {
      if (err) {
        return console.error(err);
      }

      results = _.sortBy(results, 'CategoryName');
      Common.sharedCursor.set('yelpCategories', results);
      app.cursor.commit();
    });
  };

  Common.getUpdatedQueryString = function (newParameters) {
    const currentQuery = Common.getQueryParameters();
    let updatedQuery = _.extend({}, currentQuery, newParameters);

    // remove null values, but not undefined values
    updatedQuery = _.omitBy(updatedQuery, (value, key) => _.isNull(value) || _.isEmpty(key) || (_.isArray(value) && _.isEmpty(value)));

    return updatedQuery;
  };

  Common.getQueryParameters = function () {
    return querystring.parse(window.location.search.substr(1));
  };

  Common.forceDeliverChargedLeadFromAd = function () {
    // only force a charge if no charge has been forced, and the user came from an ad
    if (Common.userCameFromFacebookAd() && !Common.haveForcedFirstCharge()) {
      app.models.agentPicker.deliverChargedLeadForCurrentAgent({}, (err) => {
        if (!err) {
          Common.setForcedFirstCharge(true);
        }
      });
    }
  };

  Common.shareAgentOnTwitter = function (e) {
    e.stopPropagation();
    const agentData = Common.getCurrentAgent();

    const text = `Discover your dream home with ${agentData.FirstName} ${agentData.LastName}.`;
    // var text = `View beautiful homes for sale by ${agentData.FirstName} ${agentData.LastName}`;
    const shareUrl = `${window.location.origin}/${agentData.CustomURL}`;

    const base = 'https://twitter.com/intent/tweet?';

    const data = {
      text,
      url: shareUrl,
      hashtags: 'homeasap',
      via: 'homeasap',
    };

    const url = base + querystring.stringify(data);
    window.open(url, '_blank', 'menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=600,width=600');
  };

  Common.shareAgentOnFacebook = function () {
    const agentUrl = Common.getCurrentAgent().CustomURL;
    const shareUrl = `${window.location.origin}/${agentUrl}`;

    FB.ui({
      method: 'share',
      href: shareUrl,
    }, (...args) => {
      console.log('SHARE RESPONSE', args);
    });
  };

  Common.shareAgentViaEmail = function (e) {
    e.stopPropagation();
    const agentData = Common.getCurrentAgent();
    const subject = `Here's ${agentData.FirstName}'s digital business card!`;
    const body = encodeURIComponent(`Hi, check out ${agentData.FirstName}'s digital business card! Bookmark it for easy reference.\r\n\r\nhttps://${window.location.host}/${agentData.CustomURL}`);

    window.open(`mailto:?subject=${subject}&body=${body}`, '_blank');
  };

  Common.shareAgentViaSMS = function (e) {
    e.stopPropagation();
    const agentData = Common.getCurrentAgent();
    const body = `Hi, check out ${agentData.FirstName}'s digital business card! https://${window.location.host}/${agentData.CustomURL}`;

    window.open(`sms:?&body=${body}`, '_blank');
  };

  Common.fetchTermsTemplate = function (templateId, callback = () => {}) {
    app.api.fetchTermsTemplate(templateId, callback);
  };

  Common.printListingById = function (id) {
    window.open(BASE_API_URL.concat('listings/pdf/', id, '?agentId=', Common.getAgentId() || ''), '_blank');
    app.actions.analytics.sendEvent('listing', 'print', Common.getAgentId(), '');
  };

  Common.restoreSearchParams = function () {
    const params = Common.getQueryParameters();
    _.each(params, (value, key) => {
      const existing = app.actions.menu.sharedMenuCursor.get(key);
      if (_.isEmpty(existing) && !_.isEmpty(value)) {
        if (key == 'keywords' && typeof value === 'string') {
          value = [value];
        }
        if (key == 'homeTypesArray') {
          if (typeof value === 'string') {
            value = [Number(value)];
          } else {
            value = (value || []).map((item) => Number(item));
          }
        }
        if (key == 'specialFinancing') {
          if (typeof value === 'string') {
            value = [value];
          } else {
            value = value || [];
          }
        }
        app.actions.menu.sharedMenuCursor.set(key, value);
      }
    });
    app.cursor.commit();
  };

  Common.copyUrl = function () {
    copy(window.location.href);
  };

  Common.shortenUrl = function (url = location.href, callback) {
    app.api.shortenUrl(url, (err, shortUrl) => {
      callback && callback(err, shortUrl);
    });
  };

  Common.submitHomeValue = function (data, callback) {
    callback();
  };

  Common.showHomeWorthModal = function () {
    app.cursor.set(['layout', 'homeWorthModal'], true);
    app.cursor.commit();
  };

  Common.showDreamsweepsModal = function () {
    window.sessionStorageAlias.removeItem(Common.FLAG_FORCED_DREAM_SWEEPS);

    app.cursor.set(['layout', 'dreamsweepsModal'], true);
    if (app.cursor.get(['shared', 'dreamsweeps']) === null) {
      app.api.getDreamsweeps((err, res) => {
        if (!err && res) {
          app.cursor.set(['shared', 'dreamsweeps'], res);
        } else {
          app.cursor.set(['shared', 'dreamsweeps'], false);
        }
        app.cursor.commit();
      });
    }
    app.cursor.commit();
  };

  Common.hideHomeWorthModal = function () {
    app.cursor.set(['layout', 'homeWorthModal'], false);
    app.cursor.commit();
  };

  Common.hideDreamsweepsModal = function () {
    app.cursor.set(['layout', 'dreamsweepsModal'], false);
    app.cursor.commit();
  };

  Common.showDreamsweepsSuccessModal = function () {
    app.cursor.set(['layout', 'dreamsweepsModal'], 'success');
    app.cursor.commit();
  };

  Common.showLendingTreeModal = function (widgetType) {
    app.cursor.set(['layout', 'lendingTreeModal'], widgetType || true);
    app.cursor.commit();
  };

  Common.hideLendingTreeModal = function () {
    app.cursor.set(['layout', 'lendingTreeModal'], false);
    app.cursor.commit();
  };

  Common.showPayPerClickModal = function () {
    app.cursor.set(['layout', 'payPerClickModal'], true);
    app.cursor.commit();
  };

  Common.hidePayPerClickModal = function () {
    app.cursor.set(['layout', 'payPerClickModal'], false);
    app.cursor.commit();
  };

  Common.showRateplugLandingModal = function () {
    app.cursor.set(['layout', 'rateplugLandingModal'], true);
    app.cursor.commit();
  };

  Common.hideRateplugLandingModal = function () {
    app.cursor.set(['layout', 'rateplugLandingModal'], false);
    app.cursor.commit();
  };

  Common.showRateplugWelcomeModal = function () {
    app.cursor.set(['layout', 'rateplugWelcomeModal'], true);
    app.cursor.commit();
  };

  Common.hideRateplugWelcomeModal = function () {
    app.cursor.set(['layout', 'rateplugWelcomeModal'], false);
    app.cursor.commit();
  };

  Common.showRateplugSpecialFinancingModal = function () {
    app.cursor.set(['layout', 'rateplugSpecialFinancingModal'], true);
    app.cursor.commit();
  };

  Common.hideRateplugSpecialFinancingModal = function () {
    app.cursor.set(['layout', 'rateplugSpecialFinancingModal'], false);
    app.cursor.commit();
  };

  Common.showRateplugSpecialFinancingMissingModal = function () {
    if (!app.cursor.get(['layout', 'rateplugSpecialFinancingMissingModal'])) {
      app.cursor.set(['layout', 'rateplugSpecialFinancingMissingModal'], true);
      app.cursor.commit();
    }
  };

  Common.hideRateplugSpecialFinancingMissingModal = function () {
    if (app.cursor.get(['layout', 'rateplugSpecialFinancingMissingModal'])) {
      app.cursor.set(['layout', 'rateplugSpecialFinancingMissingModal'], false);
      app.cursor.commit();
    }
  };

  Common.getListingMlsId = function (listing) {
    const agentData = Common.getAgentData();
    if (!agentData || !agentData.MlsId) {
      return null;
    }
    const agentMlsIds = [agentData.MlsId];
    const listingMlsIds = listing && listing.MlsIds || [];

    if (listingMlsIds.indexOf(agentData.MlsId) !== -1) {
      return agentData.MlsId;
    }

    for (const affiliatedAgent of agentData.SelfAffiliations || []) {
      if (agentMlsIds.indexOf(affiliatedAgent.MlsId) === -1) {
        agentMlsIds.push(affiliatedAgent.MlsId);
      }
    }

    for (const mlsId of listingMlsIds) {
      if (agentMlsIds.indexOf(mlsId) > -1) {
        return mlsId;
      }
    }
    return agentData.MlsId;
  };

  return Common;
};
